# 仪表板真实数据实现指南

## 当前状态

目前仪表板页面使用模拟数据显示，以避免API调用错误。为了显示真实的业务数据，有以下几种实现方案：

## 方案一：前端组合现有API数据（推荐）

### 实现思路
利用现有的客户、账单、还款API接口，在前端计算统计数据。

### 代码实现

```typescript
// apps/web-antd/src/views/dashboard/analytics/index.vue

import { getCustomerListApi } from '#/api/modules/customer';
import { getInvoiceListApi } from '#/api/modules/invoice';
import { getPaymentListApi } from '#/api/modules/payment';

// 获取真实仪表板数据
const fetchDashboardData = async () => {
  try {
    loading.value = true;
    
    // 并行获取所有数据
    const [customersResponse, invoicesResponse, paymentsResponse] = await Promise.all([
      getCustomerListApi({ per_page: 1000 }),
      getInvoiceListApi({ per_page: 1000 }),
      getPaymentListApi({ per_page: 1000 })
    ]);
    
    const customers = customersResponse.data;
    const invoices = invoicesResponse.data;
    const payments = paymentsResponse.data;
    
    // 计算统计数据
    const totalDebt = invoices
      .filter(invoice => invoice.status !== 'paid')
      .reduce((sum, invoice) => sum + (parseFloat(invoice.amount) - parseFloat(invoice.paid_amount)), 0);
    
    const today = new Date().toISOString().split('T')[0];
    const todayPayments = payments
      .filter(payment => payment.payment_date === today)
      .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    
    const overdueInvoices = invoices.filter(invoice => 
      invoice.status === 'overdue' || 
      (invoice.due_date && new Date(invoice.due_date) < new Date() && invoice.status !== 'paid')
    );
    
    const activeCustomers = customers.filter(customer => {
      // 有未付清账单的客户视为活跃客户
      return invoices.some(invoice => 
        invoice.customer_id === customer.id && invoice.status !== 'paid'
      );
    });
    
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const monthlyInvoices = invoices.filter(invoice => {
      const invoiceDate = new Date(invoice.created_at);
      return invoiceDate.getMonth() + 1 === currentMonth && 
             invoiceDate.getFullYear() === currentYear;
    });
    
    const monthlyPaidInvoices = monthlyInvoices.filter(invoice => invoice.status === 'paid');
    const monthlyPaymentRate = monthlyInvoices.length > 0 
      ? monthlyPaidInvoices.length / monthlyInvoices.length 
      : 0;
    
    dashboardData.value = {
      total_debt: totalDebt.toFixed(2),
      today_payments: todayPayments.toFixed(2),
      overdue_invoices_count: overdueInvoices.length,
      active_customers_count: activeCustomers.length,
      monthly_new_invoices: monthlyInvoices.length,
      monthly_payment_rate: monthlyPaymentRate,
    };
    
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
    // 使用模拟数据作为fallback
    dashboardData.value = {
      total_debt: '0.00',
      today_payments: '0.00',
      overdue_invoices_count: 0,
      active_customers_count: 0,
      monthly_new_invoices: 0,
      monthly_payment_rate: 0,
    };
  } finally {
    loading.value = false;
  }
};
```

### 优点
- 无需修改后端
- 使用真实业务数据
- 可以立即实施

### 缺点
- 需要获取大量数据进行计算
- 性能可能不如专门的统计接口

## 方案二：后端添加Dashboard API（最佳）

### Laravel后端实现

```php
// routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index']);
});

// app/Http/Controllers/DashboardController.php
<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        
        // 根据用户角色过滤数据
        $storeIds = $this->getUserStoreIds($user);
        
        // 计算总欠款
        $totalDebt = Invoice::when($storeIds, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->whereIn('status', ['unpaid', 'partially_paid', 'overdue'])
            ->selectRaw('SUM(amount - paid_amount) as total')
            ->value('total') ?? 0;
        
        // 计算今日收款
        $todayPayments = Payment::when($storeIds, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->whereDate('payment_date', Carbon::today())
            ->sum('amount');
        
        // 逾期账单数量
        $overdueInvoicesCount = Invoice::when($storeIds, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->where(function ($query) {
                $query->where('status', 'overdue')
                      ->orWhere(function ($q) {
                          $q->where('due_date', '<', Carbon::now())
                            ->whereIn('status', ['unpaid', 'partially_paid']);
                      });
            })
            ->count();
        
        // 活跃客户数（有未付清账单的客户）
        $activeCustomersCount = Customer::whereHas('invoices', function ($query) use ($storeIds) {
                $query->when($storeIds, function ($q) use ($storeIds) {
                    return $q->whereIn('store_id', $storeIds);
                })
                ->whereIn('status', ['unpaid', 'partially_paid', 'overdue']);
            })
            ->count();
        
        // 本月新增账单数
        $monthlyNewInvoices = Invoice::when($storeIds, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        
        // 本月收款完成率
        $monthlyInvoices = Invoice::when($storeIds, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
        
        $monthlyPaidInvoices = Invoice::when($storeIds, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->where('status', 'paid')
            ->count();
        
        $monthlyPaymentRate = $monthlyInvoices > 0 
            ? $monthlyPaidInvoices / $monthlyInvoices 
            : 0;
        
        return response()->json([
            'success' => true,
            'data' => [
                'total_debt' => number_format($totalDebt, 2, '.', ''),
                'today_payments' => number_format($todayPayments, 2, '.', ''),
                'overdue_invoices_count' => $overdueInvoicesCount,
                'active_customers_count' => $activeCustomersCount,
                'monthly_new_invoices' => $monthlyNewInvoices,
                'monthly_payment_rate' => round($monthlyPaymentRate, 4),
            ],
            'message' => '获取仪表板数据成功'
        ]);
    }
    
    private function getUserStoreIds($user)
    {
        if ($user->hasRole('admin')) {
            return null; // 管理员可以看到所有门店数据
        }
        
        return $user->stores->pluck('id')->toArray();
    }
}
```

### 优点
- 性能最佳
- 数据准确性高
- 支持权限控制

### 缺点
- 需要修改后端代码

## 推荐实施步骤

1. **立即使用**: 当前的模拟数据实现，确保页面正常显示
2. **短期方案**: 实施方案一，使用现有API组合数据
3. **长期方案**: 实施方案二，添加专门的Dashboard API

## 注意事项

- 确保数据权限控制正确
- 考虑缓存机制提升性能
- 添加错误处理和降级方案
- 定期验证统计数据的准确性
