import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    login: string; // 支持用户名或邮箱
    password: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    user: {
      id: number;
      name: string;
      username: string;
      email: string;
      roles: string[];
    };
    token: string;
  }

  /** 注册接口参数 */
  export interface RegisterParams {
    name: string; // 用户姓名
    username: string; // 用户名（支持中文）
    email: string; // 邮箱地址
    password: string; // 密码
    password_confirmation: string; // 确认密码
  }

  /** 注册接口返回值 */
  export interface RegisterResult {
    user: {
      id: number;
      name: string;
      username: string;
      email: string;
      roles: string[];
      stores: Array<{
        id: number;
        name: string;
        code: string;
      }>;
    };
    token: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }

  /** 用户信息接口返回值 */
  export interface UserInfo {
    id: number;
    name: string;
    username: string;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    roles: string[];
    stores: Array<{
      id: number;
      name: string;
      code: string;
    }>;
  }

  /** 修改密码接口参数 */
  export interface ChangePasswordParams {
    current_password: string;
    new_password: string;
    new_password_confirmation: string;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/login', data);
}

/**
 * 用户注册
 */
export async function registerApi(data: AuthApi.RegisterParams) {
  return requestClient.post<AuthApi.RegisterResult>('/register', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/refresh-token', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/logout', {
    withCredentials: true,
  });
}

/**
 * 获取当前用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<AuthApi.UserInfo>('/user');
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/user/access-codes');
}

/**
 * 修改密码
 */
export async function changePasswordApi(data: AuthApi.ChangePasswordParams) {
  return requestClient.put('/user/password', data);
}
