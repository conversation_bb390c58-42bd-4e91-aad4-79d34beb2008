import { requestClient } from '#/api/request';

export namespace AttachmentApi {
  /** 附件信息 */
  export interface Attachment {
    id: number;
    attachable_type: string;
    attachable_id: number;
    original_filename: string;
    stored_filename: string;
    file_path: string;
    file_size: number;
    mime_type: string;
    uploaded_by: number;
    created_at: string;
    updated_at: string;
    uploaded_by_user: {
      id: number;
      name: string;
    };
  }

  /** 预签名URL请求参数 */
  export interface PresignedUrlRequest {
    attachable_type: 'invoice' | 'payment';
    attachable_id: number;
    filename: string;
    file_size: number;
    mime_type: string;
  }

  /** 预签名URL响应 */
  export interface PresignedUrlResponse {
    upload_url: string;
    file_path: string;
    original_mime_type: string;
    expires_in: number;
    upload_instructions: {
      method: string;
      content_type: null;
      note: string;
    };
  }

  /** 确认上传请求参数 */
  export interface ConfirmUploadRequest {
    attachable_type: 'invoice' | 'payment';
    attachable_id: number;
    file_path: string;
    original_filename: string;
    file_size: number;
    mime_type: string;
  }

  /** 附件列表查询参数 */
  export interface AttachmentListParams {
    attachable_type: 'invoice' | 'payment';
    attachable_id: number;
  }

  /** 上传状态 */
  export interface UploadStatus {
    file: File;
    status: 'pending' | 'uploading' | 'success' | 'error';
    progress: number;
    error?: string;
    attachment?: Attachment;
  }
}

// 允许的文件类型配置
export const ALLOWED_FILE_TYPES = {
  'image/jpeg': { ext: 'jpg', icon: 'mdi:file-image', preview: true },
  'image/png': { ext: 'png', icon: 'mdi:file-image', preview: true },
  'image/gif': { ext: 'gif', icon: 'mdi:file-image', preview: true },
  'image/webp': { ext: 'webp', icon: 'mdi:file-image', preview: true },
  'application/pdf': { ext: 'pdf', icon: 'mdi:file-pdf-box', preview: false },
  'application/msword': { ext: 'doc', icon: 'mdi:file-word', preview: false },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { ext: 'docx', icon: 'mdi:file-word', preview: false },
  'application/vnd.ms-excel': { ext: 'xls', icon: 'mdi:file-excel', preview: false },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { ext: 'xlsx', icon: 'mdi:file-excel', preview: false },
  'text/plain': { ext: 'txt', icon: 'mdi:file-document', preview: false },
} as const;

// 最大文件大小 (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * 验证文件类型和大小
 */
export function validateFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!ALLOWED_FILE_TYPES[file.type as keyof typeof ALLOWED_FILE_TYPES]) {
    return {
      valid: false,
      error: `不支持的文件类型: ${file.type}。支持的类型：图片、PDF、Word、Excel、文本文件`,
    };
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `文件大小超过限制。最大允许 ${(MAX_FILE_SIZE / 1024 / 1024).toFixed(1)}MB`,
    };
  }

  return { valid: true };
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 获取文件图标
 */
export function getFileIcon(mimeType: string): string {
  return ALLOWED_FILE_TYPES[mimeType as keyof typeof ALLOWED_FILE_TYPES]?.icon || 'mdi:file';
}

/**
 * 检查文件是否可预览
 */
export function canPreviewFile(mimeType: string): boolean {
  return ALLOWED_FILE_TYPES[mimeType as keyof typeof ALLOWED_FILE_TYPES]?.preview || false;
}

/**
 * 验证确认上传参数
 */
function validateConfirmUploadParams(params: AttachmentApi.ConfirmUploadRequest): { valid: boolean; error?: string } {
  if (!params.attachable_type || !['invoice', 'payment'].includes(params.attachable_type)) {
    return { valid: false, error: 'attachable_type必须是invoice或payment' };
  }

  if (!params.attachable_id || params.attachable_id <= 0) {
    return { valid: false, error: 'attachable_id必须是正整数' };
  }

  if (!params.file_path || typeof params.file_path !== 'string') {
    return { valid: false, error: 'file_path不能为空' };
  }

  if (!params.original_filename || typeof params.original_filename !== 'string') {
    return { valid: false, error: 'original_filename不能为空' };
  }

  if (!params.file_size || params.file_size <= 0) {
    return { valid: false, error: 'file_size必须是正整数' };
  }

  if (!params.mime_type || typeof params.mime_type !== 'string') {
    return { valid: false, error: 'mime_type不能为空' };
  }

  return { valid: true };
}

/**
 * 获取预签名上传URL
 */
export async function getPresignedUrlApi(data: AttachmentApi.PresignedUrlRequest) {
  return requestClient.post<AttachmentApi.PresignedUrlResponse>('/attachments/presigned-url', data);
}

/**
 * 直接上传文件到缤纷云S4对象存储
 *
 * 重要说明：
 * - 使用PUT方法上传
 * - 不设置Content-Type头，让浏览器自动处理
 * - 支持上传进度回调
 * - 基于缤纷云S4官方SDK文档优化
 */
export async function uploadFileToStorageApi(uploadUrl: string, file: File, onProgress?: (progress: number) => void) {
  return new Promise<void>((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // 设置上传进度监听
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });
    }

    // 设置请求完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve();
      } else {
        reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`));
      }
    });

    // 设置网络错误监听
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误，上传失败'));
    });

    // 设置超时监听
    xhr.addEventListener('timeout', () => {
      reject(new Error('上传超时，请重试'));
    });

    // 配置请求
    xhr.open('PUT', uploadUrl);
    xhr.timeout = 60000; // 60秒超时

    // 关键：不设置任何Content-Type头，让浏览器自动处理
    // 这样避免签名验证失败

    // 发送文件
    xhr.send(file);
  });
}

/**
 * 确认文件上传完成
 */
export async function confirmUploadApi(data: AttachmentApi.ConfirmUploadRequest) {
  try {
    return await requestClient.post<AttachmentApi.Attachment>('/attachments', data);
  } catch (error: any) {
    console.error('确认上传API调用失败:', error);

    // 尝试提取更详细的错误信息
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
      console.error('响应头:', error.response.headers);

      // 如果后端返回了具体的验证错误信息
      if (error.response.data && error.response.data.message) {
        throw new Error(error.response.data.message);
      }

      if (error.response.data && error.response.data.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat();
        throw new Error(`验证错误: ${errorMessages.join(', ')}`);
      }
    }

    throw error;
  }
}

/**
 * 获取附件列表
 */
export async function getAttachmentListApi(params: AttachmentApi.AttachmentListParams) {
  return requestClient.get<AttachmentApi.Attachment[]>('/attachments', { params });
}

/**
 * 删除附件
 */
export async function deleteAttachmentApi(id: number) {
  return requestClient.delete(`/attachments/${id}`);
}

/**
 * 完整的文件上传流程（基于缤纷云S4优化配置）
 *
 * 实现三步上传流程：
 * 1. 获取预签名URL
 * 2. 直接上传到缤纷云S4（不设置Content-Type头）
 * 3. 确认上传完成
 */
export async function uploadFileApi(
  file: File,
  attachableType: 'invoice' | 'payment',
  attachableId: number,
  onProgress?: (progress: number) => void
): Promise<AttachmentApi.Attachment> {
  try {
    console.log('开始文件上传流程:', {
      filename: file.name,
      size: file.size,
      type: file.type,
      attachableType,
      attachableId
    });

    // 1. 验证文件
    const validation = validateFile(file);
    if (!validation.valid) {
      console.error('文件验证失败:', validation.error);
      throw new Error(validation.error);
    }

    // 2. 获取预签名URL
    console.log('步骤1: 获取预签名URL...');
    const presignedResponse = await getPresignedUrlApi({
      attachable_type: attachableType,
      attachable_id: attachableId,
      filename: file.name,
      file_size: Math.floor(file.size), // 确保是整数
      mime_type: file.type,
    });
    console.log('✅ 预签名URL获取成功');

    // 3. 上传文件到缤纷云S4（重要：不设置Content-Type头）
    console.log('步骤2: 上传文件到缤纷云S4...');
    console.log('上传URL:', presignedResponse.upload_url);
    await uploadFileToStorageApi(presignedResponse.upload_url, file, onProgress);
    console.log('✅ 文件上传成功');

    // 临时调试：尝试验证文件是否真的上传成功
    console.log('验证文件路径:', presignedResponse.file_path);

    // 4. 确认上传完成
    console.log('步骤3: 确认上传完成...');
    const confirmData = {
      attachable_type: attachableType,
      attachable_id: attachableId,
      file_path: presignedResponse.file_path,
      original_filename: file.name,
      file_size: Math.floor(file.size), // 确保是整数
      mime_type: file.type, // 根据API文档，使用原始文件的mime_type
    };
    console.log('确认上传请求数据:', confirmData);

    // 验证参数
    const paramValidation = validateConfirmUploadParams(confirmData);
    if (!paramValidation.valid) {
      console.error('确认上传参数验证失败:', paramValidation.error);
      throw new Error(`参数验证失败: ${paramValidation.error}`);
    }

    const attachment = await confirmUploadApi(confirmData);
    console.log('✅ 上传流程完成，附件ID:', attachment.id);

    return attachment;
  } catch (error) {
    console.error('文件上传流程失败:', error);

    // 提供更详细的错误信息
    if (error instanceof Error) {
      if (error.message.includes('422')) {
        throw new Error('文件上传验证失败，请检查文件参数或联系管理员');
      } else if (error.message.includes('403')) {
        throw new Error('上传权限被拒绝，请检查S4配置和权限设置');
      } else if (error.message.includes('404')) {
        throw new Error('上传地址不存在，请检查预签名URL配置');
      } else if (error.message.includes('网络错误')) {
        throw new Error('网络连接失败，请检查网络连接或稍后重试');
      }
    }

    // 如果是API响应错误，尝试提取详细错误信息
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(`上传失败: ${error.message}`);
    }

    throw error;
  }
}
