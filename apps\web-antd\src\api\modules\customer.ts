import { requestClient } from '#/api/request';

export namespace CustomerApi {
  /** 客户信息 */
  export interface Customer {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    address?: string;
    id_card?: string;
    remarks?: string;
    created_at: string;
    updated_at: string;
  }

  /** 客户列表查询参数 */
  export interface CustomerListParams {
    search?: string;
    page?: number;
    per_page?: number;
  }

  /** 分页响应格式 */
  export interface PaginatedResponse<T> {
    current_page: number;
    data: T[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
  }

  /** 客户表单数据 */
  export interface CustomerFormData {
    name: string;
    phone?: string;
    email?: string;
    address?: string;
    id_card?: string;
    remarks?: string;
  }

  /** 客户详情（包含账单和还款记录） */
  export interface CustomerDetail extends Customer {
    invoices: Array<{
      id: number;
      invoice_number: string;
      amount: string;
      paid_amount: string;
      status: string;
      invoice_date: string;
      due_date: string;
    }>;
    payments: Array<{
      id: number;
      payment_number: string;
      amount: string;
      payment_date: string;
      payment_method: string;
    }>;
  }

  /** 客户欠款汇总 */
  export interface CustomerDebt {
    customer: {
      id: number;
      name: string;
      phone: string;
    };
    traditional_debt: number;
    actual_debt: number;
    discount_summary: {
      total_count: number;
      total_amount: number;
      by_type: {
        [key: string]: {
          count: number;
          amount: number;
        };
      };
    };
    store_debt_info: {
      total_invoices: number;
      unpaid_invoices: number;
      total_amount: number;
      paid_amount: number;
      discount_amount: number;
      traditional_debt: number;
      actual_debt: number;
      discount_rate: number;
      store_count: number;
    };
    accessible_stores: number[];
    unpaid_invoices: Array<{
      id: number;
      invoice_number: string;
      store_id: number;
      amount: string;
      paid_amount: string;
      discount_amount: string;
      actual_remaining: string;
      status: string;
      invoice_date: string;
      due_date: string;
      has_discounts: boolean;
      store?: {
        id: number;
        name: string;
      };
    }>;
  }
}

/**
 * 获取客户列表
 */
export async function getCustomerListApi(params?: CustomerApi.CustomerListParams) {
  return requestClient.get<CustomerApi.PaginatedResponse<CustomerApi.Customer>>('/customers', {
    params,
  });
}

/**
 * 创建客户
 */
export async function createCustomerApi(data: CustomerApi.CustomerFormData) {
  return requestClient.post<CustomerApi.Customer>('/customers', data);
}

/**
 * 获取客户详情
 */
export async function getCustomerDetailApi(id: number) {
  return requestClient.get<CustomerApi.CustomerDetail>(`/customers/${id}`);
}

/**
 * 更新客户信息
 */
export async function updateCustomerApi(id: number, data: CustomerApi.CustomerFormData) {
  return requestClient.put<CustomerApi.Customer>(`/customers/${id}`, data);
}

/**
 * 删除客户
 */
export async function deleteCustomerApi(id: number) {
  return requestClient.delete(`/customers/${id}`);
}

/**
 * 获取客户欠款汇总
 */
export async function getCustomerDebtApi(id: number) {
  return requestClient.get<CustomerApi.CustomerDebt>(`/customers/${id}/debt`);
}
