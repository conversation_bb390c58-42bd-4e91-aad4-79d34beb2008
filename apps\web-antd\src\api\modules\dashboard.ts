import { requestClient } from '#/api/request';

export namespace DashboardApi {
  /** 后端返回的仪表板概览数据结构 */
  export interface DashboardOverviewResponse {
    summary: {
      total_customers: number;
      total_invoices: number;
      total_payments: number;
      total_stores: number;
    };
    financial: {
      total_invoice_amount: string;
      total_paid_amount: string;
      total_outstanding_amount: string;
      total_payment_amount: string;
    };
    invoice_status_distribution: {
      unpaid: number;
      partially_paid: number;
      paid: number;
      overdue: number;
    };
  }

  /** 前端使用的仪表板数据格式 */
  export interface DashboardData {
    total_debt: string;
    today_payments: string;
    overdue_invoices_count: number;
    active_customers_count: number;
    monthly_new_invoices: number;
    monthly_payment_rate: number;
  }
}

/**
 * 获取仪表板概览数据
 */
export async function getDashboardDataApi(): Promise<DashboardApi.DashboardData> {
  const response = await requestClient.get<DashboardApi.DashboardOverviewResponse>('/dashboard/overview');

  // 将后端数据映射到前端期望的格式
  return {
    total_debt: response.financial.total_outstanding_amount,
    today_payments: response.financial.total_payment_amount, // 暂时使用总支付金额，后续可优化
    overdue_invoices_count: response.invoice_status_distribution.overdue,
    active_customers_count: response.summary.total_customers,
    monthly_new_invoices: response.summary.total_invoices, // 暂时使用总账单数，后续可优化
    monthly_payment_rate: response.summary.total_invoices > 0
      ? response.invoice_status_distribution.paid / response.summary.total_invoices
      : 0,
  };
}
