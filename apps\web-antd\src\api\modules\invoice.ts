import { requestClient } from '#/api/request';

export namespace InvoiceApi {
  /** 账单信息 */
  export interface Invoice {
    id: number;
    invoice_number: string;
    store_id: number;
    customer_id: number;
    created_by: number;
    amount: string;
    paid_amount: string;
    status: 'unpaid' | 'partially_paid' | 'paid' | 'overdue';
    invoice_date: string;
    due_date: string;
    description?: string;
    created_at: string;
    updated_at: string;
    store: {
      id: number;
      name: string;
    };
    customer: {
      id: number;
      name: string;
      phone: string;
    };
    created_by: {
      id: number;
      name: string;
    };
  }

  /** 账单列表查询参数 */
  export interface InvoiceListParams {
    store_id?: number;
    customer_id?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
    page?: number;
    per_page?: number;
  }

  /** 分页响应格式 */
  export interface PaginatedResponse<T> {
    current_page: number;
    data: T[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
  }

  /** 账单表单数据 */
  export interface InvoiceFormData {
    store_id: number;
    customer_id: number;
    amount?: number; // 传统模式必填，明细模式可选
    invoice_date: string;
    due_date?: string;
    description?: string;
    items?: InvoiceItemCreateData[]; // 明细模式时使用
  }

  /** 账单详情（包含还款分配记录） */
  export interface InvoiceDetail extends Invoice {
    payment_allocations: Array<{
      id: number;
      amount: string;
      allocated_at: string;
      payment: {
        id: number;
        payment_number: string;
        payment_date: string;
        payment_method: string;
      };
      allocated_by: {
        id: number;
        name: string;
      };
    }>;
  }

  /** 账单明细 */
  export interface InvoiceItem {
    id: number;
    invoice_id: number;
    item_name: string;
    item_description?: string;
    quantity: string;
    unit_price: string;
    subtotal: string;
    sort_order: number;
    created_at: string;
    updated_at: string;
  }

  /** 账单明细表单数据 */
  export interface InvoiceItemFormData {
    item_name: string;
    item_description?: string;
    quantity: number;
    unit_price: number;
    sort_order?: number;
  }

  /** 创建账单时的明细项数据 */
  export interface InvoiceItemCreateData {
    item_name: string;
    item_description?: string;
    quantity: number;
    unit_price: number;
    sort_order?: number;
  }
}

/**
 * 获取账单列表
 */
export async function getInvoiceListApi(params?: InvoiceApi.InvoiceListParams) {
  return requestClient.get<InvoiceApi.PaginatedResponse<InvoiceApi.Invoice>>('/invoices', {
    params,
  });
}

/**
 * 创建账单
 */
export async function createInvoiceApi(data: InvoiceApi.InvoiceFormData) {
  return requestClient.post<InvoiceApi.Invoice>('/invoices', data);
}

/**
 * 获取账单详情
 */
export async function getInvoiceDetailApi(id: number) {
  return requestClient.get<InvoiceApi.InvoiceDetail>(`/invoices/${id}`);
}

/**
 * 更新账单信息
 */
export async function updateInvoiceApi(id: number, data: Partial<InvoiceApi.InvoiceFormData>) {
  return requestClient.put<InvoiceApi.Invoice>(`/invoices/${id}`, data);
}

/**
 * 删除账单
 */
export async function deleteInvoiceApi(id: number) {
  return requestClient.delete(`/invoices/${id}`);
}

/**
 * 获取账单明细列表
 */
export async function getInvoiceItemsApi(invoiceId: number) {
  return requestClient.get<InvoiceApi.InvoiceItem[]>(`/invoices/${invoiceId}/items`);
}

/**
 * 添加账单明细
 */
export async function createInvoiceItemApi(invoiceId: number, data: InvoiceApi.InvoiceItemFormData) {
  return requestClient.post<InvoiceApi.InvoiceItem>(`/invoices/${invoiceId}/items`, data);
}

/**
 * 更新账单明细项
 */
export async function updateInvoiceItemApi(
  itemId: number,
  data: Partial<InvoiceApi.InvoiceItemFormData>
) {
  return requestClient.put<InvoiceApi.InvoiceItem>(`/invoice-items/${itemId}`, data);
}

/**
 * 删除账单明细项
 */
export async function deleteInvoiceItemApi(itemId: number) {
  return requestClient.delete(`/invoice-items/${itemId}`);
}
