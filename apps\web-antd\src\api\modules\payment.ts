import { requestClient } from '#/api/request';

export namespace PaymentApi {
  /** 还款记录 */
  export interface Payment {
    id: number;
    payment_number: string;
    store_id: number;
    customer_id: number;
    received_by: number;
    amount: string;
    allocated_amount: string;
    payment_date: string;
    payment_method: 'cash' | 'bank_transfer' | 'wechat' | 'alipay' | 'other';
    reference_number?: string;
    remarks?: string;
    created_at: string;
    updated_at: string;
    store: {
      id: number;
      name: string;
    };
    customer: {
      id: number;
      name: string;
      phone: string;
    };
    received_by: {
      id: number;
      name: string;
    };
  }

  /** 还款列表查询参数 */
  export interface PaymentListParams {
    store_id?: number;
    customer_id?: number;
    payment_method?: string;
    start_date?: string;
    end_date?: string;
    page?: number;
    per_page?: number;
  }

  /** 分页响应格式 */
  export interface PaginatedResponse<T> {
    current_page: number;
    data: T[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
  }

  /** 还款表单数据 */
  export interface PaymentFormData {
    store_id: number;
    customer_id: number;
    amount: number;
    payment_date: string;
    payment_method: 'cash' | 'bank_transfer' | 'wechat' | 'alipay' | 'other';
    reference_number?: string;
    remarks?: string;
    allocations?: Array<{
      invoice_id: number;
      amount: number;
    }>;
    discount?: {
      type: 'discount' | 'promotion' | 'bad_debt';
      amount: number;
      reason: string;
    };
  }

  /** 还款详情（包含分配记录） */
  export interface PaymentDetail extends Payment {
    allocations: Array<{
      id: number;
      amount: string;
      allocated_at: string;
      invoice: {
        id: number;
        invoice_number: string;
        amount: string;
        paid_amount: string;
        status: string;
      };
      allocated_by: {
        id: number;
        name: string;
      };
    }>;
  }

  /** 还款分配数据 */
  export interface PaymentAllocationData {
    invoice_id: number;
    amount: number;
  }
}

/**
 * 获取还款记录列表
 */
export async function getPaymentListApi(params?: PaymentApi.PaymentListParams) {
  return requestClient.get<PaymentApi.PaginatedResponse<PaymentApi.Payment>>('/payments', {
    params,
  });
}

/**
 * 创建还款记录
 */
export async function createPaymentApi(data: PaymentApi.PaymentFormData) {
  return requestClient.post<PaymentApi.PaymentDetail>('/payments', data);
}

/**
 * 获取还款详情
 */
export async function getPaymentDetailApi(id: number) {
  return requestClient.get<PaymentApi.PaymentDetail>(`/payments/${id}`);
}

/**
 * 分配还款到账单
 */
export async function allocatePaymentApi(id: number, data: PaymentApi.PaymentAllocationData) {
  return requestClient.post(`/payments/${id}/allocate`, data);
}

/**
 * 删除还款记录
 */
export async function deletePaymentApi(id: number) {
  return requestClient.delete(`/payments/${id}`);
}
