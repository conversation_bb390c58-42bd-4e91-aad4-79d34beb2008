import { requestClient } from '#/api/request';

export namespace ReportApi {
  /** 收入统计查询参数 */
  export interface RevenueReportParams {
    period: 'day' | 'week' | 'month' | 'year';
    store_id?: number;
    start_date?: string;
    end_date?: string;
  }

  /** 收入统计响应数据 */
  export interface RevenueReportData {
    period: string;
    date_range: {
      start: string;
      end: string;
    };
    total_revenue: string;
    total_invoices_amount: string;
    payment_completion_rate: number;
    period_data: Array<{
      period: string;
      revenue: string;
      invoices_amount: string;
      paid_amount: string;
      completion_rate: number;
    }>;
  }

  /** 逾期统计查询参数 */
  export interface OverdueReportParams {
    days?: number;
    store_id?: number;
    start_date?: string;
    end_date?: string;
    customer_id?: number;
  }

  /** 逾期统计响应数据 */
  export interface OverdueReportData {
    filters: {
      days_threshold: number;
      store_id?: number;
      date_range?: {
        start: string;
        end: string;
      };
      customer_id?: number;
    };
    summary: {
      total_overdue_count: number;
      total_overdue_amount: string;
      average_overdue_days: number;
    };
    groups: Array<{
      range: string;
      count: number;
      amount: string;
      percentage: number;
    }>;
  }

  /** 客户排行榜查询参数 */
  export interface CustomerRankingParams {
    limit?: number;
    store_id?: number;
    min_debt?: number;
    include_zero_debt?: boolean;
  }

  /** 客户排行榜响应数据 */
  export interface CustomerRankingData {
    filters: {
      limit: number;
      store_id?: number;
      min_debt: string;
      include_zero_debt: boolean;
    };
    customers: Array<{
      id: number;
      name: string;
      phone: string;
      email?: string;
      total_debt: string;
      unpaid_invoices_count: number;
      overdue_invoices_count: number;
      latest_invoice_date: string;
      earliest_overdue_date?: string;
    }>;
  }
}

/**
 * 获取收入统计报表
 */
export async function getRevenueReportApi(params: ReportApi.RevenueReportParams) {
  return requestClient.get<ReportApi.RevenueReportData>('/reports/revenue', {
    params,
  });
}

/**
 * 获取逾期账单统计
 */
export async function getOverdueReportApi(params?: ReportApi.OverdueReportParams) {
  return requestClient.get<ReportApi.OverdueReportData>('/reports/overdue', {
    params,
  });
}

/**
 * 获取客户欠款排行榜
 */
export async function getCustomerRankingApi(params?: ReportApi.CustomerRankingParams) {
  return requestClient.get<ReportApi.CustomerRankingData>('/reports/customer-ranking', {
    params,
  });
}
