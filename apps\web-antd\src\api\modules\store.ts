import { requestClient } from '#/api/request';

export namespace StoreApi {
  /** 门店信息 */
  export interface Store {
    id: number;
    name: string;
    code: string;
    address?: string;
    phone?: string;
    description?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }

  /** 门店表单数据 */
  export interface StoreFormData {
    name: string;
    code: string;
    address?: string;
    phone?: string;
    description?: string;
    is_active?: boolean;
  }
}

/**
 * 获取门店列表
 */
export async function getStoreListApi() {
  return requestClient.get<StoreApi.Store[]>('/stores');
}

/**
 * 创建门店
 */
export async function createStoreApi(data: StoreApi.StoreFormData) {
  return requestClient.post<StoreApi.Store>('/stores', data);
}

/**
 * 获取门店详情
 */
export async function getStoreDetailApi(id: number) {
  return requestClient.get<StoreApi.Store>(`/stores/${id}`);
}

/**
 * 更新门店信息
 */
export async function updateStoreApi(id: number, data: StoreApi.StoreFormData) {
  return requestClient.put<StoreApi.Store>(`/stores/${id}`, data);
}

/**
 * 删除门店
 */
export async function deleteStoreApi(id: number) {
  return requestClient.delete(`/stores/${id}`);
}
