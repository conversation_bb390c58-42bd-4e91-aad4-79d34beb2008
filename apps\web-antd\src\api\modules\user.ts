import { requestClient } from '#/api/request';

export namespace UserApi {
  /** 用户信息 */
  export interface User {
    id: number;
    name: string;
    username: string;
    email: string;
    created_at: string;
    roles: Array<{
      id: number;
      name: string;
      slug: string;
    }>;
    stores: Array<{
      id: number;
      name: string;
      code: string;
    }>;
  }

  /** 分页响应格式 */
  export interface PaginatedResponse<T> {
    current_page: number;
    data: T[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
  }

  /** 用户列表查询参数 */
  export interface UserListParams {
    search?: string;
    role?: string;
    page?: number;
    per_page?: number;
  }

  /** 角色信息 */
  export interface Role {
    id: number;
    name: string;
    slug: string;
    description: string;
    is_system: boolean;
  }

  /** 更新用户角色数据 */
  export interface UpdateUserRolesData {
    role_ids: number[];
  }

  /** 更新用户门店权限数据 */
  export interface UpdateUserStoresData {
    stores: number[];
  }
}

/**
 * 获取用户列表
 */
export async function getUserListApi(params?: UserApi.UserListParams) {
  return requestClient.get<UserApi.PaginatedResponse<UserApi.User>>('/users', {
    params,
  });
}

/**
 * 获取用户详情
 */
export async function getUserDetailApi(id: number) {
  return requestClient.get<UserApi.User>(`/users/${id}`);
}

/**
 * 更新用户角色
 */
export async function updateUserRolesApi(id: number, data: UserApi.UpdateUserRolesData) {
  return requestClient.put<UserApi.User>(`/users/${id}/roles`, data);
}

/**
 * 更新用户门店权限
 */
export async function updateUserStoresApi(id: number, data: UserApi.UpdateUserStoresData) {
  return requestClient.put<UserApi.User>(`/users/${id}/stores`, data);
}

/**
 * 获取角色列表
 */
export async function getRoleListApi() {
  return requestClient.get<UserApi.Role[]>('/roles');
}
