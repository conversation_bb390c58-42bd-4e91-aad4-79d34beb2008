<script setup lang="ts">
import { ref, computed } from 'vue';
import { List, Button, Image, Modal, message, Popconfirm, Tag } from 'ant-design-vue';
import { AntDesignDeleteOutlined, AntDesignEyeOutlined } from '@vben/icons';
import dayjs from 'dayjs';

import {
  deleteAttachmentApi,
  formatFileSize,
  getFileIcon,
  canPreviewFile,
  type AttachmentApi,
} from '#/api/modules/attachment';

interface Props {
  attachments: AttachmentApi.Attachment[];
  loading?: boolean;
  showActions?: boolean;
}

interface Emits {
  (e: 'delete', attachment: AttachmentApi.Attachment): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: true,
});

const emit = defineEmits<Emits>();

// 预览相关
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 删除状态
const deletingIds = ref<Set<number>>(new Set());

// 获取文件下载URL
const getDownloadUrl = (attachment: AttachmentApi.Attachment) => {
  // 使用实际的文件路径构建URL，适配缤纷云S3存储
  if (attachment.file_path) {
    // 如果file_path是完整的URL，直接使用
    if (attachment.file_path.startsWith('http')) {
      return attachment.file_path;
    }
    // 否则构建S3访问URL
    return `https://gxhpimg.s3.bitiful.net/${attachment.file_path}`;
  }
  // 备用方案：使用API下载接口
  return `${import.meta.env.VITE_API_URL}/attachments/${attachment.id}/download`;
};

// 获取缩略图URL
const getThumbnailUrl = (attachment: AttachmentApi.Attachment) => {
  if (canPreviewFile(attachment.mime_type)) {
    return getDownloadUrl(attachment);
  }
  return null;
};

// 预览文件
const previewFile = (attachment: AttachmentApi.Attachment) => {
  if (canPreviewFile(attachment.mime_type)) {
    previewImage.value = getDownloadUrl(attachment);
    previewTitle.value = attachment.original_filename;
    previewVisible.value = true;
  } else {
    // 对于不能预览的文件，直接下载
    downloadFile(attachment);
  }
};

// 下载文件
const downloadFile = (attachment: AttachmentApi.Attachment) => {
  const link = document.createElement('a');
  link.href = getDownloadUrl(attachment);
  link.download = attachment.original_filename;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 删除附件
const handleDelete = async (attachment: AttachmentApi.Attachment) => {
  try {
    deletingIds.value.add(attachment.id);
    await deleteAttachmentApi(attachment.id);
    message.success(`附件 "${attachment.original_filename}" 删除成功`);
    emit('delete', attachment);
    emit('refresh');
  } catch (error) {
    console.error('删除附件失败:', error);
    message.error('删除附件失败');
  } finally {
    deletingIds.value.delete(attachment.id);
  }
};

// 获取文件类型标签颜色
const getFileTypeColor = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return 'green';
  if (mimeType === 'application/pdf') return 'red';
  if (mimeType.includes('word')) return 'blue';
  if (mimeType.includes('excel') || mimeType.includes('sheet')) return 'orange';
  return 'default';
};

// 获取文件扩展名
const getFileExtension = (filename: string) => {
  return filename.split('.').pop()?.toUpperCase() || '';
};

// 获取上传者显示名称
const getUploaderName = (attachment: AttachmentApi.Attachment) => {
  // 优先使用 uploaded_by.name（根据实际API响应格式）
  // 使用类型断言处理可能的对象格式
  if (attachment.uploaded_by && typeof attachment.uploaded_by === 'object') {
    const uploadedBy = attachment.uploaded_by as any;
    if (uploadedBy.name) {
      return uploadedBy.name;
    }
  }

  // 兼容性支持：使用 uploaded_by_user.name
  if (attachment.uploaded_by_user?.name) {
    return attachment.uploaded_by_user.name;
  }

  // 如果uploaded_by_user存在但没有name，尝试其他字段
  if (attachment.uploaded_by_user) {
    const user = attachment.uploaded_by_user as any;
    if (typeof user === 'object' && user !== null) {
      // 尝试常见的用户名字段
      const name = user.username || user.realName || user.display_name || user.nickname;
      if (name) return name;

      // 如果有ID，显示为用户ID
      if (user.id) return `用户 ${user.id}`;
    }
  }

  // 如果uploaded_by是数字ID，显示为用户ID
  if (attachment.uploaded_by && typeof attachment.uploaded_by === 'number') {
    return `用户 ${attachment.uploaded_by}`;
  }

  // 如果uploaded_by是字符串，直接显示
  if (attachment.uploaded_by && typeof attachment.uploaded_by === 'string') {
    return attachment.uploaded_by;
  }

  // 最后的fallback
  return '未知用户';
};
</script>

<template>
  <div class="attachment-list">
    <!-- 附件列表 -->
    <List
      :data-source="attachments"
      :loading="loading"
      item-layout="horizontal"
      :locale="{ emptyText: '暂无附件' }"
    >
      <template #renderItem="{ item: attachment }">
        <List.Item>
          <template #actions>
            <div v-if="showActions" class="flex items-center space-x-1">
              <!-- 预览/下载按钮 -->
              <Button
                type="text"
                size="small"
                @click="previewFile(attachment)"
                :title="canPreviewFile(attachment.mime_type) ? '预览图片' : '下载文件'"
                class="hover:bg-primary/10 hover:text-primary transition-colors duration-200"
              >
                <AntDesignEyeOutlined v-if="canPreviewFile(attachment.mime_type)" class="text-base" />
                <span v-else class="text-base">📥</span>
              </Button>

              <!-- 删除按钮 -->
              <Popconfirm
                title="确定要删除这个附件吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(attachment)"
                placement="topRight"
              >
                <Button
                  type="text"
                  size="small"
                  danger
                  :loading="deletingIds.has(attachment.id)"
                  :title="'删除附件'"
                  class="hover:bg-red-50 hover:text-red-600 transition-colors duration-200"
                >
                  <AntDesignDeleteOutlined class="text-base" />
                </Button>
              </Popconfirm>
            </div>
          </template>

          <List.Item.Meta>
            <template #avatar>
              <div class="relative w-14 h-14 rounded-lg overflow-hidden border border-border/50 shadow-sm">
                <!-- 图片缩略图 -->
                <div v-if="canPreviewFile(attachment.mime_type)" class="w-full h-full">
                  <Image
                    :src="getThumbnailUrl(attachment)"
                    :alt="attachment.original_filename"
                    :preview="false"
                    class="w-full h-full object-cover"
                    :fallback="'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHZpZXdCb3g9IjAgMCA1NiA1NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjU2IiBoZWlnaHQ9IjU2IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yOCAzNkMzMC4yMDkxIDM2IDMyIDM0LjIwOTEgMzIgMzJDMzIgMjkuNzkwOSAzMC4yMDkxIDI4IDI4IDI4QzI1Ljc5MDkgMjggMjQgMjkuNzkwOSAyNCAzMkMyNCAzNC4yMDkxIDI1Ljc5MDkgMzYgMjggMzZaIiBmaWxsPSIjQkZCRkJGIi8+CjxwYXRoIGQ9Ik00MiAyMEgxNEMxMi44OTU0IDIwIDEyIDIwLjg5NTQgMTIgMjJWNDJDMTIgNDMuMTA0NiAxMi44OTU0IDQ0IDE0IDQ0SDQyQzQzLjEwNDYgNDQgNDQgNDMuMTA0NiA0NCA0MlYyMkM0NCAyMC44OTU0IDQzLjEwNDYgMjAgNDIgMjBaIiBzdHJva2U9IiNCRkJGQkYiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8L3N2Zz4K'"
                  />
                  <!-- 图片类型标识 -->
                  <div class="absolute top-1 right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-xs">🖼️</span>
                  </div>
                </div>

                <!-- 非图片文件图标 -->
                <div v-else class="w-full h-full bg-gradient-to-br from-accent/10 to-accent/20 flex flex-col items-center justify-center">
                  <div class="text-2xl mb-1">
                    <span v-if="attachment.mime_type === 'application/pdf'">📄</span>
                    <span v-else-if="attachment.mime_type.includes('word')">📝</span>
                    <span v-else-if="attachment.mime_type.includes('excel') || attachment.mime_type.includes('sheet')">📊</span>
                    <span v-else-if="attachment.mime_type === 'text/plain'">📋</span>
                    <span v-else>📎</span>
                  </div>
                  <div class="text-xs font-bold text-muted-foreground">
                    {{ getFileExtension(attachment.original_filename) }}
                  </div>
                </div>
              </div>
            </template>

            <template #title>
              <div class="flex items-center space-x-3">
                <span class="font-semibold text-foreground text-base truncate flex-1">{{ attachment.original_filename }}</span>
                <Tag :color="getFileTypeColor(attachment.mime_type)" size="small" class="font-medium">
                  {{ getFileExtension(attachment.original_filename) }}
                </Tag>
              </div>
            </template>

            <template #description>
              <div class="space-y-2 text-sm">
                <div class="flex items-center space-x-6 text-muted-foreground">
                  <div class="flex items-center space-x-1">
                    <span class="text-xs">📏</span>
                    <span class="font-medium">{{ formatFileSize(attachment.file_size) }}</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <span class="text-xs">👤</span>
                    <span>{{ getUploaderName(attachment) }}</span>
                  </div>
                </div>
                <div class="flex items-center space-x-1 text-muted-foreground">
                  <span class="text-xs">🕒</span>
                  <span>{{ dayjs(attachment.created_at).format('YYYY-MM-DD HH:mm:ss') }}</span>
                </div>
              </div>
            </template>
          </List.Item.Meta>
        </List.Item>
      </template>
    </List>

    <!-- 图片预览模态框 -->
    <Modal
      v-model:open="previewVisible"
      :title="previewTitle"
      :footer="null"
      width="90%"
      :style="{ maxWidth: '800px' }"
      centered
      class="image-preview-modal"
    >
      <div class="text-center p-4">
        <div class="relative inline-block">
          <Image
            :src="previewImage"
            :alt="previewTitle"
            class="max-w-full max-h-[70vh] rounded-lg shadow-lg"
            :preview="false"
            :placeholder="true"
          />
        </div>
      </div>
      <div class="mt-6 flex justify-center space-x-3">
        <Button
          type="primary"
          size="large"
          @click="downloadFile(attachments.find(a => getDownloadUrl(a) === previewImage)!)"
          class="px-6"
        >
          <span class="mr-2">📥</span>
          下载原图
        </Button>
        <Button
          size="large"
          @click="previewVisible = false"
          class="px-6"
        >
          关闭
        </Button>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.attachment-list {
  @apply w-full;
}

/* 列表项样式 */
:deep(.ant-list-item) {
  @apply border-b border-border/30 last:border-b-0 py-5 px-2 rounded-lg hover:bg-accent/5 transition-all duration-200;
}

:deep(.ant-list-item:hover) {
  @apply shadow-sm transform translate-y-[-1px];
}

:deep(.ant-list-item-meta-avatar) {
  @apply mr-5;
}

:deep(.ant-list-item-meta-content) {
  @apply flex-1;
}

:deep(.ant-list-item-action) {
  @apply ml-4;
}

/* 图片预览模态框样式 */
:deep(.image-preview-modal .ant-modal-content) {
  @apply overflow-hidden;
}

:deep(.image-preview-modal .ant-modal-body) {
  @apply p-0;
}

/* 缩略图加载动画 */
:deep(.ant-image-img) {
  @apply transition-all duration-300;
}

:deep(.ant-image-placeholder) {
  @apply bg-accent/10;
}

/* 夜间模式适配 */
.dark :deep(.ant-list-item) {
  @apply border-border;
}

.dark :deep(.ant-list-empty-text) {
  @apply text-muted-foreground;
}
</style>
