<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Card, Divider, Empty, Spin } from 'ant-design-vue';
import { AntDesignPlusOutlined } from '@vben/icons';

import AttachmentUpload from './AttachmentUpload.vue';
import AttachmentList from './AttachmentList.vue';
import {
  getAttachmentListApi,
  type AttachmentApi,
} from '#/api/modules/attachment';

interface Props {
  attachableType: 'invoice' | 'payment';
  attachableId: number;
  title?: string;
  showUpload?: boolean;
  showTitle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '附件管理',
  showUpload: true,
  showTitle: true,
});

// 状态管理
const loading = ref(false);
const attachments = ref<AttachmentApi.Attachment[]>([]);

// 计算属性
const hasAttachments = computed(() => attachments.value.length > 0);
const attachmentCount = computed(() => attachments.value.length);

// 获取附件列表
const fetchAttachments = async () => {
  try {
    loading.value = true;
    const response = await getAttachmentListApi({
      attachable_type: props.attachableType,
      attachable_id: props.attachableId,
    });
    attachments.value = response;
  } catch (error) {
    console.error('获取附件列表失败:', error);
    attachments.value = [];
  } finally {
    loading.value = false;
  }
};

// 处理上传成功
const handleUploadSuccess = (attachment: AttachmentApi.Attachment) => {
  attachments.value.unshift(attachment);
};

// 处理上传错误
const handleUploadError = (error: string) => {
  console.error('上传失败:', error);
};

// 处理删除附件
const handleDeleteAttachment = (attachment: AttachmentApi.Attachment) => {
  const index = attachments.value.findIndex(a => a.id === attachment.id);
  if (index > -1) {
    attachments.value.splice(index, 1);
  }
};

// 刷新附件列表
const handleRefresh = () => {
  fetchAttachments();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchAttachments();
});

// 暴露方法给父组件
defineExpose({
  refresh: fetchAttachments,
  attachments: computed(() => attachments.value),
  count: attachmentCount,
});
</script>

<template>
  <Card :title="showTitle ? title : undefined" class="attachment-manager">
    <template v-if="showTitle" #title>
      <div class="flex items-center space-x-2">
        <AntDesignPlusOutlined class="text-lg" />
        <span>{{ title }}</span>
        <span v-if="attachmentCount > 0" class="text-sm text-muted-foreground">
          ({{ attachmentCount }} 个文件)
        </span>
      </div>
    </template>

    <div class="space-y-8">
      <!-- 上传区域 -->
      <div v-if="showUpload" class="upload-section">
        <AttachmentUpload
          :attachable-type="attachableType"
          :attachable-id="attachableId"
          @success="handleUploadSuccess"
          @error="handleUploadError"
        />
      </div>

      <!-- 分隔线 -->
      <Divider v-if="showUpload && hasAttachments" class="my-8">
        <template #default>
          <div class="flex items-center space-x-2 text-muted-foreground">
            <span>📁</span>
            <span class="font-medium">已上传的附件</span>
            <span class="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
              {{ attachmentCount }} 个文件
            </span>
          </div>
        </template>
      </Divider>

      <!-- 附件列表 -->
      <div class="attachment-list-container">
        <Spin :spinning="loading" size="large">
          <div v-if="hasAttachments" class="bg-background border border-border/30 rounded-xl shadow-sm overflow-hidden">
            <AttachmentList
              :attachments="attachments"
              :loading="loading"
              @delete="handleDeleteAttachment"
              @refresh="handleRefresh"
            />
          </div>

          <!-- 空状态 -->
          <div
            v-else-if="!loading"
            class="text-center py-16 bg-gradient-to-br from-accent/5 to-accent/10 rounded-xl border border-dashed border-border/50"
          >
            <div class="text-6xl mb-4">📁</div>
            <div class="text-lg font-medium text-muted-foreground mb-2">暂无附件</div>
            <div v-if="showUpload" class="text-sm text-muted-foreground">
              请使用上方的上传区域添加相关文件
            </div>
          </div>
        </Spin>
      </div>
    </div>
  </Card>
</template>

<style scoped>
.attachment-manager {
  @apply w-full;
}

.upload-section {
  @apply relative;
}

.attachment-list-container {
  @apply min-h-32;
}

/* 卡片样式优化 */
:deep(.ant-card) {
  @apply shadow-sm border-border/50;
}

:deep(.ant-card-head) {
  @apply border-b border-border/30 bg-gradient-to-r from-background to-accent/5;
}

:deep(.ant-card-body) {
  @apply p-8;
}

/* 分隔线样式 */
:deep(.ant-divider) {
  @apply border-border/30;
}

:deep(.ant-divider-inner-text) {
  @apply text-muted-foreground font-medium bg-background px-4;
}

/* 加载动画样式 */
:deep(.ant-spin-container) {
  @apply transition-all duration-300;
}

:deep(.ant-spin-blur) {
  @apply opacity-50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.ant-card-body) {
    @apply p-4;
  }

  .space-y-8 > * + * {
    @apply mt-6;
  }
}

/* 夜间模式适配 */
.dark :deep(.ant-card) {
  @apply bg-background border-border/50 shadow-lg;
}

.dark :deep(.ant-card-head) {
  @apply bg-gradient-to-r from-background to-accent/10 border-border/30;
}

.dark :deep(.ant-card-head-title) {
  @apply text-foreground;
}
</style>
