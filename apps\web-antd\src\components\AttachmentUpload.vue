<script setup lang="ts">
import { ref, computed } from 'vue';
import { Upload, Button, Progress, message } from 'ant-design-vue';
import { AntDesignPlusOutlined } from '@vben/icons';

import {
  uploadFileApi,
  validateFile,
  formatFileSize,
  type AttachmentApi,
} from '#/api/modules/attachment';

interface Props {
  attachableType: 'invoice' | 'payment';
  attachableId: number;
  multiple?: boolean;
  disabled?: boolean;
}

interface Emits {
  (e: 'success', attachment: AttachmentApi.Attachment): void;
  (e: 'error', error: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  disabled: false,
});

const emit = defineEmits<Emits>();

// 上传状态管理
const uploading = ref(false);
const uploadProgress = ref<Record<string, number>>({});

// 文件上传处理
const handleUpload = async (file: File) => {
  // 验证文件
  const validation = validateFile(file);
  if (!validation.valid) {
    message.error(validation.error);
    emit('error', validation.error!);
    return false;
  }

  const fileKey = `${file.name}_${file.size}_${file.lastModified}`;
  
  try {
    uploading.value = true;
    uploadProgress.value[fileKey] = 0;

    // 上传文件
    const attachment = await uploadFileApi(
      file,
      props.attachableType,
      props.attachableId,
      (progress) => {
        uploadProgress.value[fileKey] = progress;
      }
    );

    message.success(`文件 "${file.name}" 上传成功`);
    emit('success', attachment);
    
    // 清理进度
    delete uploadProgress.value[fileKey];
  } catch (error) {
    console.error('文件上传失败:', error);
    let errorMessage = '上传失败';

    if (error instanceof Error) {
      // 检查是否是服务器配置错误
      if (error.message.includes('PortableVisibilityConverter') || error.message.includes('Flysystem')) {
        errorMessage = '服务器文件存储配置错误，请联系管理员';
      } else if (error.message.includes('500')) {
        errorMessage = '服务器内部错误，请稍后重试';
      } else if (error.message.includes('403') || error.message.includes('权限被拒绝')) {
        errorMessage = '缤纷云S4权限被拒绝，请检查配置和权限设置';
      } else if (error.message.includes('404') || error.message.includes('不存在')) {
        errorMessage = '上传地址不存在，请检查预签名URL配置';
      } else if (error.message.includes('网络错误') || error.message.includes('CORS')) {
        errorMessage = '缤纷云S4网络连接失败，请检查网络或CORS配置';
      } else if (error.message.includes('超时')) {
        errorMessage = '上传超时，请检查网络连接或稍后重试';
      } else {
        errorMessage = error.message;
      }
    }

    message.error(`文件 "${file.name}" 上传失败: ${errorMessage}`);
    emit('error', errorMessage);
    
    // 清理进度
    delete uploadProgress.value[fileKey];
  } finally {
    // 检查是否还有其他文件在上传
    if (Object.keys(uploadProgress.value).length === 0) {
      uploading.value = false;
    }
  }

  return false; // 阻止默认上传行为
};

// 计算总体上传进度
const totalProgress = computed(() => {
  const progresses = Object.values(uploadProgress.value);
  if (progresses.length === 0) return 0;
  return Math.round(progresses.reduce((sum, p) => sum + p, 0) / progresses.length);
});

// 是否有文件正在上传
const hasUploading = computed(() => Object.keys(uploadProgress.value).length > 0);

// 拖拽上传属性
const uploadProps = {
  name: 'file',
  multiple: props.multiple,
  showUploadList: false,
  beforeUpload: handleUpload,
  disabled: props.disabled || uploading.value,
};
</script>

<template>
  <div class="attachment-upload">
    <!-- 上传区域 -->
    <Upload.Dragger v-bind="uploadProps" class="upload-dragger">
      <div class="upload-content">
        <div class="upload-icon flex justify-center">
          <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            <AntDesignPlusOutlined class="text-3xl text-primary" />
          </div>
        </div>
        <div class="upload-text">
          <p class="text-xl font-semibold text-foreground mb-2">点击或拖拽文件到此区域上传</p>
          <p class="text-sm text-muted-foreground">
            支持多种文件格式，单个文件最大 10MB
          </p>
        </div>
      </div>
    </Upload.Dragger>

    <!-- 上传进度 -->
    <div v-if="hasUploading" class="mt-6 p-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/10 dark:to-indigo-900/10 border border-border/50 rounded-lg shadow-sm">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span class="text-sm font-semibold text-foreground">正在上传文件</span>
        </div>
        <span class="text-sm font-bold text-primary">{{ totalProgress }}%</span>
      </div>
      <Progress
        :percent="totalProgress"
        :show-info="false"
        :stroke-color="totalProgress === 100 ? '#52c41a' : '#1890ff'"
        :strokeWidth="6"
        class="mb-3"
      />

      <!-- 详细进度 -->
      <div class="space-y-2">
        <div
          v-for="(progress, fileKey) in uploadProgress"
          :key="fileKey"
          class="flex items-center justify-between text-xs bg-background/50 rounded px-2 py-1"
        >
          <span class="truncate flex-1 mr-2 text-foreground font-medium">{{ fileKey.split('_')[0] }}</span>
          <span class="text-primary font-semibold">{{ progress }}%</span>
        </div>
      </div>
    </div>

    <!-- 快速上传按钮 -->
    <div class="mt-6 flex justify-center">
      <Upload v-bind="uploadProps">
        <Button
          type="primary"
          size="large"
          :loading="uploading"
          :disabled="props.disabled"
          class="px-8 h-11 shadow-md hover:shadow-lg transition-all duration-200"
        >
          <AntDesignPlusOutlined class="mr-2" />
          选择文件上传
        </Button>
      </Upload>
    </div>

    <!-- 使用说明 -->
    <div class="mt-4 p-4 bg-gradient-to-r from-accent/5 to-accent/10 border border-border/50 rounded-lg text-sm shadow-sm">
      <div class="font-medium mb-3 text-foreground">📁 支持的文件类型</div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-muted-foreground">
        <div class="flex items-center space-x-2">
          <span class="text-green-500">🖼️</span>
          <span>图片：JPG, PNG, GIF, WebP</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-red-500">📄</span>
          <span>文档：PDF, Word, Excel</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-blue-500">📝</span>
          <span>文本：TXT</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-orange-500">⚖️</span>
          <span>大小限制：最大 10MB</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.attachment-upload {
  @apply w-full;
}

.upload-dragger {
  @apply border-2 border-dashed border-border/60 bg-gradient-to-br from-background to-accent/5 hover:border-primary/60 hover:bg-gradient-to-br hover:from-primary/5 hover:to-primary/10 transition-all duration-300 shadow-sm hover:shadow-md;
}

.upload-dragger:hover {
  @apply border-primary/80 transform scale-[1.02];
}

.upload-content {
  @apply p-10 text-center;
}

/* 进度条自定义样式 */
:deep(.ant-progress-bg) {
  @apply transition-all duration-300;
}

/* 上传区域动画效果 */
.upload-dragger {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-dragger:active {
  @apply transform scale-[0.98];
}

.upload-icon {
  @apply mb-6 flex justify-center items-center;
}

.upload-icon > div {
  @apply flex items-center justify-center;
}

.upload-text p {
  @apply m-0;
}

/* 夜间模式适配 */
.dark .upload-dragger {
  @apply border-border bg-background;
}

.dark .upload-dragger:hover {
  @apply border-primary/50;
}
</style>
