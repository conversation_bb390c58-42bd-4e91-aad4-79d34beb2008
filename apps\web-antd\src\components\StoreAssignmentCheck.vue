<template>
  <!-- 全屏毛玻璃背景 -->
  <div
    v-if="visible"
    class="fixed inset-0 z-[9999] flex items-center justify-center bg-black/20 backdrop-blur-md"
    @click="handleBackdropClick"
  >
    <!-- 主要内容区域 -->
    <div class="text-center px-8 py-12 max-w-2xl mx-auto">
      <!-- 大标题 -->
      <div class="mb-8">
        <h1 class="text-6xl font-bold text-white mb-4 drop-shadow-lg">
          您未分配店铺
        </h1>
        <p class="text-xl text-white/90 drop-shadow-md">
          请联系管理员为您分配门店权限
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center space-x-6">
        <Button
          size="large"
          class="bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm px-8 py-3 text-lg h-auto"
          @click="handleContactAdmin"
        >
          <MdiEmailOutline class="mr-2 text-xl" />
          联系管理员
        </Button>
        <Button
          type="primary"
          size="large"
          danger
          class="px-8 py-3 text-lg h-auto"
          @click="handleLogout"
        >
          <MdiLogout class="mr-2 text-xl" />
          退出登录
        </Button>
      </div>

      <!-- 管理员提示 -->
      <p class="text-white/60 text-sm mt-8">
        如果您是管理员，此提醒可能不适用于您
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from 'ant-design-vue';
import {
  MdiEmailOutline,
  MdiLogout
} from '@vben/icons';
import { useAuthStore } from '#/store';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const authStore = useAuthStore();

// 处理背景点击（阻止关闭）
const handleBackdropClick = (event: Event) => {
  // 阻止点击背景关闭，强制用户选择操作
  event.stopPropagation();
};

// 处理联系管理员
const handleContactAdmin = () => {
  // 这里可以根据实际需求实现联系管理员的逻辑
  // 比如打开邮件客户端、显示联系方式等
  window.open('mailto:<EMAIL>?subject=申请门店权限&body=您好，我需要申请门店权限以使用系统功能。');
};

// 处理退出登录
const handleLogout = async () => {
  emit('update:visible', false);
  await authStore.logout();
};
</script>
