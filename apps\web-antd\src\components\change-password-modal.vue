<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import { Modal, Form, Input, Button, message } from 'ant-design-vue';
import { AntDesignLockOutlined } from '@vben/icons';
import { changePasswordApi, type AuthApi } from '#/api/core/auth';

interface Props {
  open: boolean;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单数据
const formData = reactive<AuthApi.ChangePasswordParams>({
  current_password: '',
  new_password: '',
  new_password_confirmation: '',
});

// 表单引用
const formRef = ref();

// 加载状态
const loading = ref(false);

// 表单验证规则
const rules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '新密码长度不能少于6位', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value && value === formData.current_password) {
          return Promise.reject('新密码不能与当前密码相同');
        }
        return Promise.resolve();
      },
      trigger: 'blur',
    },
  ],
  new_password_confirmation: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value && value !== formData.new_password) {
          return Promise.reject('确认密码与新密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur',
    },
  ],
};

// 密码强度检查
const passwordStrength = computed(() => {
  const password = formData.new_password;
  if (!password) return { level: 0, text: '', color: '' };
  
  let score = 0;
  let feedback = [];
  
  // 长度检查
  if (password.length >= 6) score += 1;
  if (password.length >= 8) score += 1;
  
  // 复杂度检查
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^a-zA-Z0-9]/.test(password)) score += 1;
  
  if (score <= 2) {
    return { level: 1, text: '弱', color: '#ff4d4f' };
  } else if (score <= 4) {
    return { level: 2, text: '中', color: '#faad14' };
  } else {
    return { level: 3, text: '强', color: '#52c41a' };
  }
});

// 处理取消
const handleCancel = () => {
  emit('update:open', false);
  resetForm();
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    current_password: '',
    new_password: '',
    new_password_confirmation: '',
  });
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    
    await changePasswordApi(formData);
    
    message.success('密码修改成功！为了安全起见，请重新登录。');
    emit('update:open', false);
    emit('success');
    resetForm();
  } catch (error: any) {
    console.error('修改密码失败:', error);
    
    // 处理API错误响应
    if (error?.response?.data?.errors) {
      const errors = error.response.data.errors;
      if (errors.current_password) {
        message.error(errors.current_password[0]);
      } else if (errors.new_password) {
        message.error(errors.new_password[0]);
      } else {
        message.error('密码修改失败，请检查输入信息');
      }
    } else {
      message.error(error?.response?.data?.message || '密码修改失败，请稍后重试');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Modal
    :open="props.open"
    title="修改密码"
    :confirm-loading="loading"
    :mask-closable="false"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <template #icon>
      <AntDesignLockOutlined />
    </template>
    
    <Form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="mt-4"
    >
      <Form.Item label="当前密码" name="current_password">
        <Input.Password
          v-model:value="formData.current_password"
          placeholder="请输入当前密码"
          size="large"
          :maxlength="50"
        />
      </Form.Item>
      
      <Form.Item label="新密码" name="new_password">
        <Input.Password
          v-model:value="formData.new_password"
          placeholder="请输入新密码（至少6位）"
          size="large"
          :maxlength="50"
        />
        <!-- 密码强度指示器 -->
        <div v-if="formData.new_password" class="mt-2">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-500">密码强度:</span>
            <span class="text-sm font-medium" :style="{ color: passwordStrength.color }">
              {{ passwordStrength.text }}
            </span>
          </div>
          <div class="mt-1 flex gap-1">
            <div
              v-for="i in 3"
              :key="i"
              class="h-1 flex-1 rounded"
              :class="{
                'bg-red-200': passwordStrength.level < i && passwordStrength.level > 0,
                'bg-red-500': passwordStrength.level >= i && passwordStrength.level === 1,
                'bg-yellow-500': passwordStrength.level >= i && passwordStrength.level === 2,
                'bg-green-500': passwordStrength.level >= i && passwordStrength.level === 3,
                'bg-gray-200': passwordStrength.level === 0,
              }"
            />
          </div>
        </div>
      </Form.Item>
      
      <Form.Item label="确认新密码" name="new_password_confirmation">
        <Input.Password
          v-model:value="formData.new_password_confirmation"
          placeholder="请再次输入新密码"
          size="large"
          :maxlength="50"
        />
      </Form.Item>
    </Form>
    
    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
      <div class="text-sm text-blue-600">
        <div class="font-medium mb-1">🔒 安全提示：</div>
        <ul class="text-xs space-y-1 ml-4">
          <li>• 密码长度至少6位字符</li>
          <li>• 建议包含大小写字母、数字和特殊字符</li>
          <li>• 修改成功后需要重新登录</li>
        </ul>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
