import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:account-group',
      order: 2,
      title: $t('page.customer.title'),
    },
    name: 'Customer',
    path: '/customer',
    children: [
      {
        name: 'CustomerList',
        path: '/customer',
        component: () => import('#/views/customer/index.vue'),
        meta: {
          affixTab: false,
          icon: 'mdi:account-group',
          title: $t('page.customer.list'),
        },
      },
      {
        name: 'CustomerDetail',
        path: '/customer/detail/:id',
        component: () => import('#/views/customer/detail.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.customer.detail'),
        },
      },
    ],
  },
];

export default routes;
