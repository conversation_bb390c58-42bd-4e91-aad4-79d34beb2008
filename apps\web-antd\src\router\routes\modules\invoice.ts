import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:file-document-outline',
      order: 3,
      title: $t('page.invoice.title'),
    },
    name: 'Invoice',
    path: '/invoice',
    children: [
      {
        name: 'InvoiceList',
        path: '/invoice',
        component: () => import('#/views/invoice/index.vue'),
        meta: {
          affixTab: false,
          icon: 'mdi:file-document-outline',
          title: $t('page.invoice.list'),
        },
      },
      {
        name: 'InvoiceDetail',
        path: '/invoice/detail/:id',
        component: () => import('#/views/invoice/detail.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.invoice.detail'),
        },
      },
    ],
  },
];

export default routes;
