import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:cash-multiple',
      order: 4,
      title: $t('page.payment.title'),
    },
    name: 'Payment',
    path: '/payment',
    children: [
      {
        name: 'PaymentList',
        path: '/payment',
        component: () => import('#/views/payment/index.vue'),
        meta: {
          affixTab: false,
          icon: 'mdi:cash-multiple',
          title: $t('page.payment.list'),
        },
      },
      {
        name: 'PaymentDetail',
        path: '/payment/detail/:id',
        component: () => import('#/views/payment/detail.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.payment.detail'),
        },
      },
    ],
  },
];

export default routes;
