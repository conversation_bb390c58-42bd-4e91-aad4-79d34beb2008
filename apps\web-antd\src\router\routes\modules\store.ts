import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:store',
      order: 5,
      title: $t('page.store.title'),
      // 仅管理员可见
      authority: ['admin'],
    },
    name: 'Store',
    path: '/store',
    children: [
      {
        name: 'StoreList',
        path: '/store',
        component: () => import('#/views/store/index.vue'),
        meta: {
          affixTab: false,
          icon: 'mdi:store',
          title: $t('page.store.list'),
          authority: ['admin'],
        },
      },
    ],
  },
];

export default routes;
