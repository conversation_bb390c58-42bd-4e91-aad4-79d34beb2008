import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

import { getUserInfoApi, loginApi, logoutApi } from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const loginResult = await loginApi(params);

      // Laravel API返回格式: { user: {...}, token: "..." }
      if (loginResult && loginResult.token) {
        const accessToken = loginResult.token;
        accessStore.setAccessToken(accessToken);

        // 登录成功后，获取完整的用户信息（包括门店信息）
        userInfo = await fetchUserInfo();

        // 设置访问权限（使用用户角色作为权限码）
        const accessCodes = userInfo.roles || [];
        accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(
                userInfo.homePath || preferences.app.defaultHomePath,
              );
        }

        if (userInfo?.realName) {
          notification.success({
            description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3,
            message: $t('authentication.loginSuccess'),
          });
        }
      }
    } catch (error: any) {
      console.error('登录失败:', error);

      // 显示错误信息
      let errorMessage = '登录失败，请检查用户名和密码';
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      notification.error({
        description: errorMessage,
        duration: 5,
        message: '登录失败',
      });

      // 重新抛出错误以便组件处理
      throw error;
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    try {
      const laravelUser = await getUserInfoApi();

      // 将Laravel用户信息转换为Vben格式
      userInfo = {
        userId: laravelUser.id.toString(),
        username: laravelUser.username || laravelUser.login,
        realName: laravelUser.name,
        avatar: '', // Laravel API中没有头像字段
        desc: '', // Laravel API中没有描述字段
        homePath: '/dashboard/analytics',
        roles: laravelUser.roles || [], // 使用后端返回的角色信息
        stores: laravelUser.stores || [], // 保存用户所属门店信息
      };

      userStore.setUserInfo(userInfo);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，使用基本信息（测试用）
      userInfo = {
        userId: '1',
        username: 'admin',
        realName: '管理员',
        avatar: '',
        desc: '',
        homePath: '/dashboard/analytics',
        roles: ['admin'], // 测试用户默认为管理员
        stores: [], // 管理员可以访问所有门店
      };
      userStore.setUserInfo(userInfo);
    }
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
