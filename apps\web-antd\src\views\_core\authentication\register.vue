<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, h, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationRegister, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { notification } from 'ant-design-vue';

import { registerApi } from '#/api';

defineOptions({ name: 'Register' });

const router = useRouter();
const loading = ref(false);

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.emailTip'),
      },
      fieldName: 'email',
      label: $t('authentication.email'),
      rules: z.string().email({ message: $t('authentication.emailValidErrorTip') }).optional().or(z.literal('')),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      renderComponentContent() {
        return {
          strengthText: () => $t('authentication.passwordStrength'),
        };
      },
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.confirmPassword'),
      },
      dependencies: {
        rules(values) {
          const { password } = values;
          return z
            .string({ required_error: $t('authentication.passwordTip') })
            .min(1, { message: $t('authentication.passwordTip') })
            .refine((value) => value === password, {
              message: $t('authentication.confirmPasswordTip'),
            });
        },
        triggerFields: ['password'],
      },
      fieldName: 'confirmPassword',
      label: $t('authentication.confirmPassword'),
    },
    {
      component: 'VbenCheckbox',
      fieldName: 'agreePolicy',
      renderComponentContent: () => ({
        default: () =>
          h('span', [
            $t('authentication.agree'),
            h(
              'a',
              {
                class: 'vben-link ml-1 ',
                href: '',
              },
              `${$t('authentication.privacyPolicy')} & ${$t('authentication.terms')}`,
            ),
          ]),
      }),
      rules: z.boolean().refine((value) => !!value, {
        message: $t('authentication.agreeTip'),
      }),
    },
  ];
});

async function handleSubmit(value: Recordable<any>) {
  try {
    loading.value = true;

    // 准备注册参数，将表单数据映射到API参数格式
    const registerParams = {
      name: value.username, // 使用用户名作为姓名
      username: value.username,
      email: value.email || `${value.username}@example.com`, // 如果邮箱为空，生成默认邮箱
      password: value.password,
      password_confirmation: value.confirmPassword,
    };

    // 调用注册API
    const result = await registerApi(registerParams);

    // 注册成功处理
    if (result) {
      notification.success({
        message: '注册成功',
        description: `欢迎 ${result.user.name}！请使用您的账号密码登录系统。`,
        duration: 5,
      });

      // 跳转到登录页面，并预填充用户名
      await router.push({
        path: '/auth/login',
        query: {
          username: result.user.username,
        },
      });
    }
  } catch (error: any) {
    console.error('注册失败:', error);

    // 处理错误信息
    let errorMessage = '注册失败，请检查输入信息';

    if (error?.response?.data) {
      const errorData = error.response.data;

      // 处理Laravel验证错误格式
      if (errorData.errors) {
        const errors = Object.values(errorData.errors).flat();
        errorMessage = errors.join('；');
      } else if (errorData.message) {
        errorMessage = errorData.message;
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    notification.error({
      message: '注册失败',
      description: errorMessage,
      duration: 5,
    });
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <AuthenticationRegister
    :form-schema="formSchema"
    :loading="loading"
    @submit="handleSubmit"
  />
</template>
