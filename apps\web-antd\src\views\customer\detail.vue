<script lang="ts" setup>
import { onMounted, ref, h, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Card, Descriptions, Table, Tag, Button, Space, Statistic, Row, Col } from 'ant-design-vue';
import { AntDesignArrowLeftOutlined } from '@vben/icons';

import {
  getCustomerDetailApi,
  getCustomerDebtApi,
  type CustomerApi,
} from '#/api/modules/customer';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const debtLoading = ref(false);
const customer = ref<CustomerApi.CustomerDetail | null>(null);
const customerDebt = ref<CustomerApi.CustomerDebt | null>(null);
const error = ref<string | null>(null);

// 获取客户ID
const customerId = Number(route.params.id);

// 计算属性
const hasOverdueInvoices = computed(() => {
  if (!customerDebt.value) return false;
  const today = new Date();
  return customerDebt.value.unpaid_invoices.some(invoice => {
    const dueDate = new Date(invoice.due_date);
    return dueDate < today;
  });
});

const earliestDueDate = computed(() => {
  if (!customerDebt.value || customerDebt.value.unpaid_invoices.length === 0) return null;
  const dates = customerDebt.value.unpaid_invoices.map(invoice => new Date(invoice.due_date));
  return new Date(Math.min(...dates.map(d => d.getTime())));
});

const averageInvoiceAmount = computed(() => {
  if (!customerDebt.value || customerDebt.value.store_debt_info.total_invoices === 0) return 0;
  return customerDebt.value.store_debt_info.total_amount / customerDebt.value.store_debt_info.total_invoices;
});

// 账单状态映射
const statusMap = {
  unpaid: { text: '未付款', color: 'red' },
  partially_paid: { text: '部分付款', color: 'orange' },
  paid: { text: '已付清', color: 'green' },
  overdue: { text: '已逾期', color: 'volcano' },
};

// 支付方式映射
const paymentMethodMap = {
  cash: '现金',
  bank_transfer: '银行转账',
  wechat: '微信支付',
  alipay: '支付宝',
  other: '其他',
};

// 优惠减免类型映射
const discountTypeMap = {
  discount: '折扣优惠',
  promotion: '促销活动',
  bad_debt: '坏账处理',
};

// 获取优惠减免类型名称
const getDiscountTypeName = (type: string) => {
  return discountTypeMap[type as keyof typeof discountTypeMap] || type;
};

// 获取优惠减免类型颜色
const getDiscountTypeColor = (type: string) => {
  const colorMap = {
    discount: 'blue',
    promotion: 'green',
    bad_debt: 'orange',
  };
  return colorMap[type as keyof typeof colorMap] || 'default';
};

// 账单列表列定义
const invoiceColumns = [
  {
    title: '账单编号',
    dataIndex: 'invoice_number',
    key: 'invoice_number',
    width: 180,
  },
  {
    title: '账单金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '已付金额',
    dataIndex: 'paid_amount',
    key: 'paid_amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }: { text: keyof typeof statusMap }) => {
      const status = statusMap[text];
      return h(Tag, { color: status.color }, () => status.text);
    },
  },
  {
    title: '账单日期',
    dataIndex: 'invoice_date',
    key: 'invoice_date',
    width: 120,
  },
  {
    title: '到期日期',
    dataIndex: 'due_date',
    key: 'due_date',
    width: 120,
  },
];

// 未付清账单列定义（包含优惠减免信息）
const unpaidInvoiceColumns = [
  {
    title: '账单编号',
    dataIndex: 'invoice_number',
    key: 'invoice_number',
    width: 180,
  },
  {
    title: '门店',
    dataIndex: 'store',
    key: 'store',
    width: 120,
    customRender: ({ record }: { record: any }) => record.store?.name || `门店${record.store_id}`,
  },
  {
    title: '账单金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '已付金额',
    dataIndex: 'paid_amount',
    key: 'paid_amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '优惠减免',
    dataIndex: 'discount_amount',
    key: 'discount_amount',
    width: 120,
    customRender: ({ text, record }: { text: string; record: any }) => {
      if (record.has_discounts) {
        return h('span', { style: { color: '#52c41a' } }, `¥${text}`);
      }
      return '-';
    },
  },
  {
    title: '实际剩余',
    dataIndex: 'actual_remaining',
    key: 'actual_remaining',
    width: 120,
    customRender: ({ text }: { text: string }) =>
      h('span', { style: { color: '#cf1322', fontWeight: 'bold' } }, `¥${text}`),
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }: { text: keyof typeof statusMap }) => {
      const status = statusMap[text];
      return h(Tag, { color: status.color }, () => status.text);
    },
  },
  {
    title: '到期日期',
    dataIndex: 'due_date',
    key: 'due_date',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      const dueDate = new Date(text);
      const today = new Date();
      const isOverdue = dueDate < today;
      return h('span', {
        style: { color: isOverdue ? '#cf1322' : '#666' }
      }, text);
    },
  },
];

// 还款记录列定义
const paymentColumns = [
  {
    title: '还款编号',
    dataIndex: 'payment_number',
    key: 'payment_number',
    width: 180,
  },
  {
    title: '还款金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '还款方式',
    dataIndex: 'payment_method',
    key: 'payment_method',
    width: 120,
    customRender: ({ text }: { text: keyof typeof paymentMethodMap }) => {
      return paymentMethodMap[text] || text;
    },
  },
  {
    title: '还款日期',
    dataIndex: 'payment_date',
    key: 'payment_date',
    width: 120,
  },
];

// 获取客户详情
const fetchCustomerDetail = async () => {
  try {
    loading.value = true;
    error.value = null;
    customer.value = await getCustomerDetailApi(customerId);
  } catch (err: any) {
    console.error('获取客户详情失败:', err);
    error.value = err?.response?.data?.message || '获取客户详情失败';
  } finally {
    loading.value = false;
  }
};

// 获取客户欠款汇总
const fetchCustomerDebt = async () => {
  try {
    debtLoading.value = true;
    customerDebt.value = await getCustomerDebtApi(customerId);
  } catch (err: any) {
    console.error('获取客户欠款汇总失败:', err);
    // 如果是403错误，说明权限不足，不显示错误信息
    if (err?.response?.status !== 403) {
      error.value = err?.response?.data?.message || '获取欠款信息失败';
    }
  } finally {
    debtLoading.value = false;
  }
};

// 返回客户列表
const goBack = () => {
  router.push('/customer');
};

// 组件挂载时获取数据
onMounted(async () => {
  await fetchCustomerDetail();
  // 只有在客户详情获取成功后才获取欠款信息
  if (customer.value) {
    await fetchCustomerDebt();
  }
});
</script>

<template>
  <div class="p-4">
    <!-- 页面头部 -->
    <div class="mb-4">
      <Button @click="goBack">
        <AntDesignArrowLeftOutlined />
        返回客户列表
      </Button>
    </div>

    <div v-if="customer" class="space-y-4">
      <!-- 客户基本信息 -->
      <Card title="客户基本信息" :loading="loading">
        <Descriptions :column="2" bordered>
          <Descriptions.Item label="客户姓名">{{ customer.name }}</Descriptions.Item>
          <Descriptions.Item label="联系电话">{{ customer.phone || '-' }}</Descriptions.Item>
          <Descriptions.Item label="邮箱地址">{{ customer.email || '-' }}</Descriptions.Item>
          <Descriptions.Item label="身份证号">{{ customer.id_card || '-' }}</Descriptions.Item>
          <Descriptions.Item label="联系地址" :span="2">{{ customer.address || '-' }}</Descriptions.Item>
          <Descriptions.Item label="备注信息" :span="2">{{ customer.remarks || '-' }}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{{ new Date(customer.created_at).toLocaleString() }}</Descriptions.Item>
          <Descriptions.Item label="更新时间">{{ new Date(customer.updated_at).toLocaleString() }}</Descriptions.Item>
        </Descriptions>
      </Card>

      <!-- 错误提示 -->
      <Card v-if="error && !loading" title="错误信息">
        <div class="text-center py-8">
          <div class="text-red-500 mb-4">
            <div class="text-4xl mb-2">⚠️</div>
            <p>{{ error }}</p>
          </div>
          <Button @click="fetchCustomerDetail">重新加载</Button>
        </div>
      </Card>

      <!-- 欠款汇总 -->
      <Card title="欠款汇总" :loading="debtLoading">
        <div v-if="customerDebt">
          <!-- 主要财务指标 -->
          <Row :gutter="16" class="mb-6">
            <Col :span="6">
              <Statistic
                title="实际欠款金额"
                :value="customerDebt.actual_debt"
                prefix="¥"
                :precision="2"
                :value-style="{ color: '#cf1322', fontSize: '24px', fontWeight: 'bold' }"
              />
              <div class="text-xs text-gray-500 mt-1">含优惠减免</div>
            </Col>
            <Col :span="6">
              <Statistic
                title="传统欠款金额"
                :value="customerDebt.traditional_debt"
                prefix="¥"
                :precision="2"
                :value-style="{ color: '#fa8c16' }"
              />
              <div class="text-xs text-gray-500 mt-1">不含优惠减免</div>
            </Col>
            <Col :span="6">
              <Statistic
                title="优惠减免总额"
                :value="customerDebt.discount_summary.total_amount"
                prefix="¥"
                :precision="2"
                :value-style="{ color: '#52c41a' }"
              />
              <div class="text-xs text-gray-500 mt-1">{{ customerDebt.discount_summary.total_count }}笔减免</div>
            </Col>
            <Col :span="6">
              <Statistic
                title="优惠减免率"
                :value="customerDebt.store_debt_info.discount_rate"
                suffix="%"
                :precision="2"
                :value-style="{ color: '#1890ff' }"
              />
              <div class="text-xs text-gray-500 mt-1">节省比例</div>
            </Col>
          </Row>

          <!-- 账单统计信息 -->
          <Row :gutter="16" class="mb-6">
            <Col :span="6">
              <Statistic
                title="总账单数"
                :value="customerDebt.store_debt_info.total_invoices"
                suffix="笔"
              />
            </Col>
            <Col :span="6">
              <Statistic
                title="未付清账单"
                :value="customerDebt.store_debt_info.unpaid_invoices"
                suffix="笔"
                :value-style="{ color: customerDebt.store_debt_info.unpaid_invoices > 0 ? '#cf1322' : '#52c41a' }"
              />
              <div v-if="hasOverdueInvoices" class="text-xs text-red-500 mt-1">⚠️ 包含逾期账单</div>
            </Col>
            <Col :span="6">
              <Statistic
                title="已付金额"
                :value="customerDebt.store_debt_info.paid_amount"
                prefix="¥"
                :precision="2"
                :value-style="{ color: '#52c41a' }"
              />
            </Col>
            <Col :span="6">
              <Statistic
                title="涉及门店数"
                :value="customerDebt.store_debt_info.store_count"
                suffix="家"
              />
            </Col>
          </Row>

          <!-- 额外财务指标 -->
          <Row :gutter="16" class="mb-6">
            <Col :span="8">
              <Statistic
                title="平均账单金额"
                :value="averageInvoiceAmount"
                prefix="¥"
                :precision="2"
                :value-style="{ color: '#1890ff' }"
              />
            </Col>
            <Col :span="8">
              <div class="text-center">
                <div class="text-sm text-gray-500 mb-1">最早到期日期</div>
                <div class="text-lg font-medium" :class="{ 'text-red-500': hasOverdueInvoices }">
                  {{ earliestDueDate ? earliestDueDate.toLocaleDateString() : '-' }}
                </div>
              </div>
            </Col>
            <Col :span="8">
              <div class="text-center">
                <div class="text-sm text-gray-500 mb-1">付款完成率</div>
                <div class="text-lg font-medium text-green-600">
                  {{ customerDebt.store_debt_info.total_invoices > 0
                    ? ((customerDebt.store_debt_info.paid_amount / customerDebt.store_debt_info.total_amount) * 100).toFixed(1)
                    : 0 }}%
                </div>
              </div>
            </Col>
          </Row>

          <!-- 优惠减免明细 -->
          <div v-if="customerDebt.discount_summary.total_count > 0" class="mb-4">
            <h4 class="text-sm font-medium text-gray-700 mb-2">优惠减免明细</h4>
            <div class="flex flex-wrap gap-2">
              <Tag
                v-for="(discount, type) in customerDebt.discount_summary.by_type"
                :key="type"
                :color="getDiscountTypeColor(String(type))"
              >
                {{ getDiscountTypeName(String(type)) }}: {{ discount.count }}笔 ¥{{ discount.amount.toFixed(2) }}
              </Tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="text-center">
            <Space>
              <Button type="primary" @click="$router.push(`/invoice?customer_id=${customer.id}`)">
                查看所有账单
              </Button>
              <Button @click="$router.push(`/payment?customer_id=${customer.id}`)">
                查看还款记录
              </Button>
            </Space>
          </div>
        </div>

        <!-- 无数据提示 -->
        <div v-else-if="!debtLoading" class="text-center py-8">
          <div class="text-gray-500 mb-4">
            <div class="text-4xl mb-2">📊</div>
            <p>暂无欠款数据</p>
            <p class="text-sm">该客户目前没有未付清的账单</p>
          </div>
          <Button type="primary" @click="$router.push('/invoice/create')">
            创建账单
          </Button>
        </div>
      </Card>

      <!-- 未付清账单列表 -->
      <Card title="未付清账单" v-if="customerDebt && customerDebt.unpaid_invoices.length > 0">
        <Table
          :columns="unpaidInvoiceColumns"
          :data-source="customerDebt.unpaid_invoices"
          :pagination="false"
          :scroll="{ x: 1000 }"
          row-key="id"
        />
      </Card>

      <!-- 所有账单记录 -->
      <Card title="所有账单记录">
        <Table
          :columns="invoiceColumns"
          :data-source="customer?.invoices || []"
          :pagination="false"
          :scroll="{ x: 800 }"
          row-key="id"
        >
          <template #emptyText>
            <div class="text-center py-8">
              <div class="text-gray-500 mb-4">
                <div class="text-4xl mb-2">📄</div>
                <p>暂无账单记录</p>
                <p class="text-sm">为该客户创建第一张账单</p>
              </div>
              <Button type="primary" @click="$router.push('/invoice/create')">
                创建账单
              </Button>
            </div>
          </template>
        </Table>
      </Card>

      <!-- 还款记录 -->
      <Card title="还款记录">
        <Table
          :columns="paymentColumns"
          :data-source="customer.payments"
          :pagination="false"
          :scroll="{ x: 600 }"
          row-key="id"
        >
          <template #emptyText>
            <div class="text-center py-8">
              <p>暂无还款记录</p>
              <Button type="primary" @click="$router.push('/payment/create')">
                记录还款
              </Button>
            </div>
          </template>
        </Table>
      </Card>
    </div>
  </div>
</template>
