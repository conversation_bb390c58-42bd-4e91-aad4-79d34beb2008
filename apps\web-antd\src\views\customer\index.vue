<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { Card, Button, Input, Table, Space, Modal, Form, message, Popconfirm } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { AntDesignSearchOutlined, AntDesignPlusOutlined, AntDesignEditOutlined, AntDesignDeleteOutlined, AntDesignEyeOutlined } from '@vben/icons';


import {
  getCustomerListApi,
  createCustomerApi,
  updateCustomerApi,
  deleteCustomerApi,
  type CustomerApi,
} from '#/api/modules/customer';

const router = useRouter();

// 响应式数据
const loading = ref(false);
const customers = ref<CustomerApi.Customer[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  search: '',
});

// 客户表单
const customerForm = ref<CustomerApi.CustomerFormData>({
  name: '',
  phone: '',
  email: '',
  address: '',
  id_card: '',
  remarks: '',
});

// 表单状态
const formVisible = ref(false);
const formLoading = ref(false);
const editingCustomer = ref<CustomerApi.Customer | null>(null);

// 表格列定义
const columns = [
  {
    title: '客户姓名',
    dataIndex: 'name',
    key: 'name',
    width: 120,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 130,
  },
  {
    title: '邮箱地址',
    dataIndex: 'email',
    key: 'email',
    width: 180,
  },
  {
    title: '联系地址',
    dataIndex: 'address',
    key: 'address',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160,
    customRender: ({ text }: { text: string }) => {
      return new Date(text).toLocaleString();
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    fixed: 'right' as const,
  },
];

// 获取客户列表
const fetchCustomers = async () => {
  try {
    loading.value = true;
    const params: CustomerApi.CustomerListParams = {
      page: pagination.current,
      per_page: pagination.pageSize,
    };
    
    if (searchForm.search) {
      params.search = searchForm.search;
    }
    
    const response = await getCustomerListApi(params);
    customers.value = response.data;
    pagination.total = response.total;
    pagination.current = response.current_page;
  } catch (error) {
    console.error('获取客户列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索客户
const handleSearch = () => {
  pagination.current = 1;
  fetchCustomers();
};

// 重置搜索
const handleReset = () => {
  searchForm.search = '';
  pagination.current = 1;
  fetchCustomers();
};

// 分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchCustomers();
};

// 显示新增表单
const showAddForm = () => {
  editingCustomer.value = null;
  customerForm.value = {
    name: '',
    phone: '',
    email: '',
    address: '',
    id_card: '',
    remarks: '',
  };
  formVisible.value = true;
};

// 显示编辑表单
const showEditForm = (customer: CustomerApi.Customer) => {
  editingCustomer.value = customer;
  customerForm.value = {
    name: customer.name,
    phone: customer.phone || '',
    email: customer.email || '',
    address: customer.address || '',
    id_card: customer.id_card || '',
    remarks: customer.remarks || '',
  };
  formVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    formLoading.value = true;
    
    if (editingCustomer.value) {
      // 编辑客户
      await updateCustomerApi(editingCustomer.value.id, customerForm.value);
      message.success('客户信息更新成功');
    } else {
      // 新增客户
      await createCustomerApi(customerForm.value);
      message.success('客户创建成功');
    }
    
    formVisible.value = false;
    fetchCustomers();
  } catch (error) {
    console.error('保存客户失败:', error);
  } finally {
    formLoading.value = false;
  }
};

// 删除客户
const handleDelete = async (customer: CustomerApi.Customer) => {
  try {
    await deleteCustomerApi(customer.id);
    message.success('客户删除成功');
    fetchCustomers();
  } catch (error) {
    console.error('删除客户失败:', error);
  }
};

// 查看客户详情
const viewCustomerDetail = (customer: CustomerApi.Customer) => {
  router.push(`/customer/detail/${customer.id}`);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCustomers();
});
</script>

<template>
  <div class="p-4">
    <Card title="客户管理">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <Space>
          <Input
            v-model:value="searchForm.search"
            placeholder="搜索客户姓名、电话或身份证号"
            style="width: 300px"
            @press-enter="handleSearch"
          >
            <template #suffix>
              <AntDesignSearchOutlined />
            </template>
          </Input>
          <Button type="primary" @click="handleSearch">搜索</Button>
          <Button @click="handleReset">重置</Button>
          <Button type="primary" @click="showAddForm">
            <AntDesignPlusOutlined />
            新增客户
          </Button>
        </Space>
      </div>

      <!-- 客户列表表格 -->
      <Table
        :columns="columns"
        :data-source="customers"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1200 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <Space size="small">
              <Button type="link" size="small" @click="viewCustomerDetail(record)">
                <AntDesignEyeOutlined />
                详情
              </Button>
              <Button type="link" size="small" @click="showEditForm(record)">
                <AntDesignEditOutlined />
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个客户吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <Button type="link" size="small" danger>
                  <AntDesignDeleteOutlined />
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 客户表单弹窗 -->
    <Modal
      v-model:open="formVisible"
      :title="editingCustomer ? '编辑客户' : '新增客户'"
      :confirm-loading="formLoading"
      @ok="handleSubmit"
      @cancel="formVisible = false"
    >
      <Form :model="customerForm" layout="vertical">
        <Form.Item label="客户姓名" name="name" :rules="[{ required: true, message: '请输入客户姓名' }]">
          <Input v-model:value="customerForm.name" placeholder="请输入客户姓名" />
        </Form.Item>
        <Form.Item label="联系电话" name="phone">
          <Input v-model:value="customerForm.phone" placeholder="请输入联系电话" />
        </Form.Item>
        <Form.Item label="邮箱地址" name="email">
          <Input v-model:value="customerForm.email" placeholder="请输入邮箱地址" />
        </Form.Item>
        <Form.Item label="联系地址" name="address">
          <Input v-model:value="customerForm.address" placeholder="请输入联系地址" />
        </Form.Item>
        <Form.Item label="身份证号" name="id_card">
          <Input v-model:value="customerForm.id_card" placeholder="请输入身份证号" />
        </Form.Item>
        <Form.Item label="备注信息" name="remarks">
          <Input.TextArea v-model:value="customerForm.remarks" placeholder="请输入备注信息" :rows="3" />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
