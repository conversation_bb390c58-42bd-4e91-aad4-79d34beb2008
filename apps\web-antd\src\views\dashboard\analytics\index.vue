<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';

import { nextTick, onMounted, ref, computed, watch } from 'vue';
import { Card, Statistic, Row, Col, Spin, Button } from 'ant-design-vue';
import {
  AnalysisOverview,
} from '@vben/common-ui';
import { useUserStore } from '@vben/stores';
import {
  SvgBellIcon,
  SvgCakeIcon,
  SvgCardIcon,
  SvgDownloadIcon,
  MdiAlertCircle,
  MdiAlert,
  MdiInformation,
  MdiFileDocumentPlus,
  MdiCashMultiple,
  MdiAccountGroup,
} from '@vben/icons';

import { getDashboardDataApi, type DashboardApi } from '#/api/modules/dashboard';
import StoreAssignmentCheck from '#/components/StoreAssignmentCheck.vue';

// 响应式数据
const loading = ref(true);
const dashboardData = ref<DashboardApi.DashboardData | null>(null);
const showStoreAssignmentCheck = ref(false);

// 计算概览数据
const overviewItems = computed<AnalysisOverviewItem[]>(() => {
  if (!dashboardData.value) return [];

  return [
    {
      icon: SvgCardIcon,
      title: '总欠款',
      totalTitle: '总欠款金额',
      totalValue: parseFloat(dashboardData.value.total_debt),
      value: parseFloat(dashboardData.value.total_debt),
    },
    {
      icon: SvgCakeIcon,
      title: '今日收款',
      totalTitle: '今日收款金额',
      totalValue: parseFloat(dashboardData.value.today_payments),
      value: parseFloat(dashboardData.value.today_payments),
    },
    {
      icon: SvgDownloadIcon,
      title: '逾期账单',
      totalTitle: '逾期账单数量',
      totalValue: dashboardData.value.overdue_invoices_count,
      value: dashboardData.value.overdue_invoices_count,
    },
    {
      icon: SvgBellIcon,
      title: '活跃客户',
      totalTitle: '活跃客户数量',
      totalValue: dashboardData.value.active_customers_count,
      value: dashboardData.value.active_customers_count,
    },
  ];
});

// 获取仪表板数据
const fetchDashboardData = async () => {
  try {
    loading.value = true;
    dashboardData.value = await getDashboardDataApi();
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
    // 使用默认数据作为fallback
    dashboardData.value = {
      total_debt: '0.00',
      today_payments: '0.00',
      overdue_invoices_count: 0,
      active_customers_count: 0,
      monthly_new_invoices: 0,
      monthly_payment_rate: 0,
    };
  } finally {
    loading.value = false;
  }
};

// 检查门店分配情况
const checkStoreAssignment = () => {
  const shouldShow = sessionStorage.getItem('showStoreAssignmentCheck');

  if (shouldShow === 'true') {
    showStoreAssignmentCheck.value = true;
    // 显示后清除标记，避免重复显示
    sessionStorage.removeItem('showStoreAssignmentCheck');
  }
};

// 监听用户信息变化，确保在用户信息加载完成后再检查
const userStore = useUserStore();
watch(
  () => userStore.userInfo,
  (newUserInfo) => {
    if (newUserInfo) {
      // 用户信息加载完成后检查门店分配
      nextTick(() => {
        checkStoreAssignment();
      });
    }
  },
  { immediate: true }
);

// 组件挂载时获取数据
onMounted(() => {
  fetchDashboardData();

  // 如果用户信息已经存在，立即检查
  if (userStore.userInfo) {
    nextTick(() => {
      checkStoreAssignment();
    });
  }
});
</script>

<template>
  <div class="p-5">
    <Spin :spinning="loading">
      <!-- 概览数据 -->
      <AnalysisOverview :items="overviewItems" />

      <!-- 详细统计卡片 -->
      <Row :gutter="16" class="mt-5">
        <Col :span="12">
          <Card title="本月统计" class="h-full">
            <Row :gutter="16">
              <Col :span="12">
                <Statistic
                  title="新增账单"
                  :value="dashboardData?.monthly_new_invoices || 0"
                  suffix="笔"
                />
              </Col>
              <Col :span="12">
                <Statistic
                  title="收款完成率"
                  :value="((dashboardData?.monthly_payment_rate || 0) * 100).toFixed(1)"
                  suffix="%"
                  :value-style="{ color: '#3f8600' }"
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col :span="12">
          <Card title="快速操作" class="h-full">
            <div class="space-y-2">
              <Button type="primary" block @click="$router.push('/invoice')">
                <MdiFileDocumentPlus class="mr-2" />
                创建账单
              </Button>
              <Button block @click="$router.push('/payment')">
                <MdiCashMultiple class="mr-2" />
                记录还款
              </Button>
              <Button block @click="$router.push('/customer')">
                <MdiAccountGroup class="mr-2" />
                客户管理
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      <!-- 提示信息 -->
      <Card title="系统提醒" class="mt-5" v-if="dashboardData">
        <div class="space-y-2">
          <div v-if="dashboardData.overdue_invoices_count > 0" class="text-orange-600">
            <MdiAlertCircle class="mr-2" />
            您有 {{ dashboardData.overdue_invoices_count }} 笔逾期账单需要处理
          </div>
          <div v-if="parseFloat(dashboardData.total_debt) > 100000" class="text-red-600">
            <MdiAlert class="mr-2" />
            总欠款金额较高，建议加强催收工作
          </div>
          <div v-if="dashboardData.monthly_payment_rate < 0.8" class="text-yellow-600">
            <MdiInformation class="mr-2" />
            本月收款完成率偏低，请关注账单回收情况
          </div>
        </div>
      </Card>
    </Spin>

    <!-- 门店分配检查提示 -->
    <StoreAssignmentCheck
      v-model:visible="showStoreAssignmentCheck"
    />
  </div>
</template>
