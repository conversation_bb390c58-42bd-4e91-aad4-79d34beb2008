<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Card, Descriptions, Table, Tag, Button, Space, Statistic, Row, Col, Modal, Form, Input, message } from 'ant-design-vue';
import AttachmentManager from '#/components/AttachmentManager.vue';
import { AntDesignArrowLeftOutlined, AntDesignPlusOutlined, AntDesignEditOutlined, AntDesignDeleteOutlined } from '@vben/icons';

import {
  getInvoiceDetailApi,
  getInvoiceItemsApi,
  createInvoiceItemApi,
  updateInvoiceItemApi,
  deleteInvoiceItemApi,
  type InvoiceApi,
} from '#/api/modules/invoice';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const invoice = ref<InvoiceApi.InvoiceDetail | null>(null);
const paymentAllocations = ref<InvoiceApi.InvoiceDetail['payment_allocations']>([]);
const invoiceItems = ref<InvoiceApi.InvoiceItem[]>([]);

// 表单状态
const itemFormVisible = ref(false);
const itemFormLoading = ref(false);
const editingItem = ref<InvoiceApi.InvoiceItem | null>(null);

// 明细表单
const itemForm = ref<InvoiceApi.InvoiceItemFormData>({
  item_name: '',
  item_description: '',
  quantity: 0,
  unit_price: 0,
});

// 获取账单ID
const invoiceId = Number(route.params.id);

// 状态映射
const statusMap = {
  unpaid: { text: '未付款', color: 'red' },
  partially_paid: { text: '部分付款', color: 'orange' },
  paid: { text: '已付清', color: 'green' },
  overdue: { text: '已逾期', color: 'volcano' },
};

// 支付方式映射
const paymentMethodMap = {
  cash: '现金',
  bank_transfer: '银行转账',
  wechat: '微信支付',
  alipay: '支付宝',
  other: '其他',
};

// 还款分配记录列定义
const allocationColumns = [
  {
    title: '还款编号',
    dataIndex: ['payment', 'payment_number'],
    key: 'payment_number',
    width: 180,
  },
  {
    title: '分配金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '还款方式',
    dataIndex: ['payment', 'payment_method'],
    key: 'payment_method',
    width: 120,
    customRender: ({ text }: { text: keyof typeof paymentMethodMap }) => {
      return paymentMethodMap[text] || text;
    },
  },
  {
    title: '还款日期',
    dataIndex: ['payment', 'payment_date'],
    key: 'payment_date',
    width: 120,
  },
  {
    title: '分配时间',
    dataIndex: 'allocated_at',
    key: 'allocated_at',
    width: 160,
    customRender: ({ text }: { text: string }) => {
      return new Date(text).toLocaleString();
    },
  },
  {
    title: '操作人',
    dataIndex: ['allocated_by', 'name'],
    key: 'allocated_by_name',
    width: 100,
  },
];

// 账单明细列定义
const itemColumns = [
  {
    title: '商品名称',
    dataIndex: 'item_name',
    key: 'item_name',
    width: 200,
  },
  {
    title: '商品描述',
    dataIndex: 'item_description',
    key: 'item_description',
    width: 150,
    customRender: ({ text }: { text: string }) => text || '-',
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 100,
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '小计',
    dataIndex: 'subtotal',
    key: 'subtotal',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right' as const,
  },
];

// 获取账单详情
const fetchInvoiceDetail = async () => {
  try {
    loading.value = true;
    // 同时获取账单详情和明细列表
    const [detailResponse, itemsResponse] = await Promise.all([
      getInvoiceDetailApi(invoiceId),
      getInvoiceItemsApi(invoiceId),
    ]);
    invoice.value = detailResponse;
    // 从账单详情中提取还款分配记录
    paymentAllocations.value = detailResponse.payment_allocations || [];
    // 设置明细列表
    invoiceItems.value = itemsResponse;
  } catch (error) {
    console.error('获取账单详情失败:', error);
    message.error('获取账单详情失败');
  } finally {
    loading.value = false;
  }
};

// 显示新增明细表单
const showAddItemForm = () => {
  editingItem.value = null;
  itemForm.value = {
    item_name: '',
    item_description: '',
    quantity: 0,
    unit_price: 0,
  };
  itemFormVisible.value = true;
};

// 显示编辑明细表单
const showEditItemForm = (item: InvoiceApi.InvoiceItem) => {
  editingItem.value = item;
  itemForm.value = {
    item_name: item.item_name,
    item_description: item.item_description || '',
    quantity: parseFloat(item.quantity),
    unit_price: parseFloat(item.unit_price),
  };
  itemFormVisible.value = true;
};

// 提交明细表单
const handleItemSubmit = async () => {
  try {
    itemFormLoading.value = true;

    if (editingItem.value) {
      // 编辑明细
      await updateInvoiceItemApi(editingItem.value.id, itemForm.value);
      message.success('明细更新成功');
    } else {
      // 新增明细
      await createInvoiceItemApi(invoiceId, itemForm.value);
      message.success('明细添加成功');
    }

    itemFormVisible.value = false;
    fetchInvoiceDetail();
  } catch (error) {
    console.error('保存明细失败:', error);
    message.error('保存明细失败');
  } finally {
    itemFormLoading.value = false;
  }
};

// 删除明细
const handleDeleteItem = async (item: InvoiceApi.InvoiceItem) => {
  try {
    await deleteInvoiceItemApi(item.id);
    message.success('明细删除成功');
    fetchInvoiceDetail();
  } catch (error) {
    console.error('删除明细失败:', error);
    message.error('删除明细失败');
  }
};

// 返回账单列表
const goBack = () => {
  router.push('/invoice');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchInvoiceDetail();
});
</script>

<template>
  <div class="p-4">
    <!-- 页面头部 -->
    <div class="mb-4">
      <Button @click="goBack">
        <AntDesignArrowLeftOutlined />
        返回账单列表
      </Button>
    </div>

    <div v-if="invoice" class="space-y-4">
      <!-- 账单基本信息 -->
      <Card title="账单基本信息" :loading="loading">
        <Row :gutter="16" class="mb-4">
          <Col :span="6">
            <Statistic
              title="账单金额"
              :value="invoice.amount"
              prefix="¥"
              :value-style="{ color: '#1890ff' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="已付金额"
              :value="invoice.paid_amount"
              prefix="¥"
              :value-style="{ color: '#52c41a' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="剩余金额"
              :value="(parseFloat(invoice.amount) - parseFloat(invoice.paid_amount)).toFixed(2)"
              prefix="¥"
              :value-style="{ color: '#f5222d' }"
            />
          </Col>
          <Col :span="6">
            <div class="text-center">
              <div class="text-gray-500 text-sm mb-1">账单状态</div>
              <Tag :color="statusMap[invoice.status]?.color || 'default'" class="text-base px-3 py-1">
                {{ statusMap[invoice.status]?.text || invoice.status }}
              </Tag>
            </div>
          </Col>
        </Row>

        <Descriptions :column="2" bordered>
          <Descriptions.Item label="账单编号">{{ invoice.invoice_number }}</Descriptions.Item>
          <Descriptions.Item label="客户姓名">{{ invoice.customer?.name || '-' }}</Descriptions.Item>
          <Descriptions.Item label="客户电话">{{ invoice.customer?.phone || '-' }}</Descriptions.Item>
          <Descriptions.Item label="所属门店">{{ invoice.store?.name || '-' }}</Descriptions.Item>
          <Descriptions.Item label="账单日期">{{ invoice.invoice_date }}</Descriptions.Item>
          <Descriptions.Item label="到期日期">{{ invoice.due_date || '-' }}</Descriptions.Item>
          <Descriptions.Item label="创建人">{{ invoice.created_by?.name || '-' }}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{{ invoice.created_at ? new Date(invoice.created_at).toLocaleString() : '-' }}</Descriptions.Item>
          <Descriptions.Item label="账单描述" :span="2">{{ invoice.description || '-' }}</Descriptions.Item>
        </Descriptions>
      </Card>

      <!-- 账单明细 -->
      <Card title="账单明细">
        <div class="mb-4">
          <Button type="primary" @click="showAddItemForm">
            <AntDesignPlusOutlined />
            添加明细
          </Button>
        </div>

        <Table
          :columns="itemColumns"
          :data-source="invoiceItems"
          :pagination="false"
          :scroll="{ x: 700 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <Space>
                <Button type="link" size="small" @click="showEditItemForm(record)">
                  <AntDesignEditOutlined />
                  编辑
                </Button>
                <Button type="link" size="small" danger @click="handleDeleteItem(record)">
                  <AntDesignDeleteOutlined />
                  删除
                </Button>
              </Space>
            </template>
          </template>
          <template #emptyText>
            <div class="text-center py-8">
              <p>暂无明细记录</p>
              <Button type="primary" @click="showAddItemForm">
                添加明细
              </Button>
            </div>
          </template>
        </Table>
      </Card>

      <!-- 还款分配记录 -->
      <Card title="还款分配记录">
        <Table
          :columns="allocationColumns"
          :data-source="paymentAllocations"
          :pagination="false"
          :scroll="{ x: 800 }"
          row-key="id"
        >
          <template #emptyText>
            <div class="text-center py-8">
              <p>暂无还款记录</p>
              <Button type="primary" @click="$router.push('/payment')">
                记录还款
              </Button>
            </div>
          </template>
        </Table>
      </Card>

      <!-- 附件管理 -->
      <AttachmentManager
        attachable-type="invoice"
        :attachable-id="invoiceId"
        title="账单附件"
      />
    </div>

    <!-- 明细表单弹窗 -->
    <Modal
      v-model:open="itemFormVisible"
      :title="editingItem ? '编辑明细' : '添加明细'"
      :confirm-loading="itemFormLoading"
      @ok="handleItemSubmit"
      @cancel="itemFormVisible = false"
    >
      <Form :model="itemForm" layout="vertical">
        <Form.Item label="商品名称" name="item_name" :rules="[{ required: true, message: '请输入商品名称' }]">
          <Input v-model:value="itemForm.item_name" placeholder="请输入商品名称" />
        </Form.Item>
        <Form.Item label="商品描述" name="item_description">
          <Input v-model:value="itemForm.item_description" placeholder="请输入商品描述（可选）" />
        </Form.Item>
        <Form.Item label="数量" name="quantity" :rules="[{ required: true, message: '请输入数量' }]">
          <Input
            v-model:value="itemForm.quantity"
            type="number"
            placeholder="请输入数量"
            min="0.001"
            step="0.001"
          />
        </Form.Item>
        <Form.Item label="单价" name="unit_price" :rules="[{ required: true, message: '请输入单价' }]">
          <Input
            v-model:value="itemForm.unit_price"
            type="number"
            placeholder="请输入单价"
            addon-before="¥"
            min="0.01"
            step="0.01"
          />
        </Form.Item>
        <Form.Item label="小计">
          <div class="text-lg font-semibold text-blue-600">
            ¥{{ (itemForm.quantity * itemForm.unit_price).toFixed(2) }}
          </div>
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
