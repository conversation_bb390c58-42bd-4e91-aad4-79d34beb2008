<script lang="ts" setup>
import { onMounted, ref, reactive, h, computed, watch } from 'vue';
import { Card, Button, Input, Table, Space, Modal, Form, message, Popconfirm, Select, DatePicker, Tag, Radio, Divider } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import {
  AntDesignSearchOutlined,
  AntDesignPlusOutlined,
  AntDesignEditOutlined,
  AntDesignDeleteOutlined,
  AntDesignEyeOutlined
} from '@vben/icons';
import dayjs from 'dayjs';

import {
  getInvoiceListApi,
  createInvoiceApi,
  updateInvoiceApi,
  deleteInvoiceApi,
  type InvoiceApi,
} from '#/api/modules/invoice';

import { getCustomerListApi, type CustomerApi } from '#/api/modules/customer';
import { getStoreListApi, type StoreApi } from '#/api/modules/store';
import { useUserStore } from '@vben/stores';
import AttachmentUpload from '#/components/AttachmentUpload.vue';

const router = useRouter();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const invoices = ref<InvoiceApi.Invoice[]>([]);
const customers = ref<CustomerApi.Customer[]>([]);
const stores = ref<StoreApi.Store[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 用户角色检查
const isAdmin = computed(() => {
  return userStore.userRoles.includes('admin');
});

const userStores = computed(() => {
  return userStore.userInfo?.stores || [];
});

// 是否显示门店选择（管理员或多门店用户显示选择框）
const showStoreSelection = computed(() => {
  return isAdmin.value || userStores.value.length > 1;
});

// 获取可选择的门店列表
const availableStores = computed(() => {
  if (isAdmin.value) {
    // 管理员可以选择所有门店
    return stores.value;
  } else {
    // 普通用户只能选择其所属门店
    return userStores.value;
  }
});

// 计算明细模式下的总金额
const totalAmount = computed(() => {
  return invoiceItems.value.reduce((total, item) => {
    return total + (item.quantity * item.unit_price);
  }, 0);
});

// 搜索表单
const searchForm = reactive({
  customer_id: undefined,
  status: undefined,
  start_date: undefined,
  end_date: undefined,
});

// 账单表单
const invoiceForm = ref<InvoiceApi.InvoiceFormData>({
  store_id: 0,
  customer_id: 0,
  amount: 0,
  invoice_date: '',
  due_date: '',
  description: '',
});

// 表单状态
const formVisible = ref(false);
const formLoading = ref(false);
const editingInvoice = ref<InvoiceApi.Invoice | null>(null);

// 附件相关状态
const attachments = ref<any[]>([]);
const attachmentUploadKey = ref(0); // 用于重置附件上传组件
const tempFiles = ref<File[]>([]); // 临时存储的文件
const uploadingFiles = ref(false); // 文件上传状态
const fileInput = ref<HTMLInputElement>(); // 文件输入引用

// 账单创建模式：'traditional' 传统模式 | 'detailed' 明细模式
const createMode = ref<'traditional' | 'detailed'>('traditional');

// 明细项列表
const invoiceItems = ref<InvoiceApi.InvoiceItemCreateData[]>([]);

// 明细项表单
const itemForm = ref<InvoiceApi.InvoiceItemCreateData>({
  item_name: '',
  item_description: '',
  quantity: 1,
  unit_price: 0,
});

// 状态映射
const statusMap = {
  unpaid: { text: '未付款', color: 'red' },
  partially_paid: { text: '部分付款', color: 'orange' },
  paid: { text: '已付清', color: 'green' },
  overdue: { text: '已逾期', color: 'volcano' },
};

// 表格列定义
const columns = [
  {
    title: '账单编号',
    dataIndex: 'invoice_number',
    key: 'invoice_number',
    width: 180,
  },
  {
    title: '客户姓名',
    dataIndex: ['customer', 'name'],
    key: 'customer_name',
    width: 120,
  },
  {
    title: '账单金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '已付金额',
    dataIndex: 'paid_amount',
    key: 'paid_amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }: { text: keyof typeof statusMap }) => {
      const status = statusMap[text];
      return h(Tag, { color: status.color }, () => status.text);
    },
  },
  {
    title: '账单日期',
    dataIndex: 'invoice_date',
    key: 'invoice_date',
    width: 120,
  },
  {
    title: '到期日期',
    dataIndex: 'due_date',
    key: 'due_date',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160,
    customRender: ({ text }: { text: string }) => {
      return new Date(text).toLocaleString();
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    fixed: 'right' as const,
  },
];

// 获取账单列表
const fetchInvoices = async () => {
  try {
    loading.value = true;
    const params: InvoiceApi.InvoiceListParams = {
      page: pagination.current,
      per_page: pagination.pageSize,
    };
    
    if (searchForm.customer_id) {
      params.customer_id = searchForm.customer_id;
    }
    if (searchForm.status) {
      params.status = searchForm.status;
    }
    if (searchForm.start_date) {
      params.start_date = searchForm.start_date;
    }
    if (searchForm.end_date) {
      params.end_date = searchForm.end_date;
    }
    
    const response = await getInvoiceListApi(params);
    // 修复：API返回的是嵌套结构，需要正确处理响应格式
    if (response && typeof response === 'object' && 'data' in response) {
      // 如果是分页响应格式
      const paginatedResponse = response as any;
      invoices.value = paginatedResponse.data || [];
      pagination.total = paginatedResponse.total || 0;
      pagination.current = paginatedResponse.current_page || 1;
    } else {
      // 如果是直接数组格式
      invoices.value = Array.isArray(response) ? response : [];
    }
  } catch (error) {
    console.error('获取账单列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取客户列表
const fetchCustomers = async () => {
  try {
    const response = await getCustomerListApi({ per_page: 1000 });
    customers.value = response.data;
  } catch (error) {
    console.error('获取客户列表失败:', error);
  }
};

// 获取门店列表（仅管理员需要获取所有门店）
const fetchStores = async () => {
  if (!isAdmin.value) {
    // 普通用户使用已有的门店信息，无需额外获取
    return;
  }

  try {
    const response = await getStoreListApi();
    stores.value = response;
  } catch (error) {
    console.error('获取门店列表失败:', error);
  }
};

// 搜索账单
const handleSearch = () => {
  pagination.current = 1;
  fetchInvoices();
};

// 重置搜索
const handleReset = () => {
  searchForm.customer_id = undefined;
  searchForm.status = undefined;
  searchForm.start_date = undefined;
  searchForm.end_date = undefined;
  pagination.current = 1;
  fetchInvoices();
};

// 分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchInvoices();
};

// 显示新增表单
const showAddForm = () => {
  editingInvoice.value = null;
  createMode.value = 'traditional'; // 默认使用传统模式

  // 根据用户门店情况设置默认门店
  let defaultStoreId = 0;
  if (!isAdmin.value && userStores.value.length === 1) {
    // 单门店用户自动设置为唯一门店
    defaultStoreId = userStores.value[0].id;
  }
  // 管理员和多门店用户不设置默认值，要求用户选择

  invoiceForm.value = {
    store_id: defaultStoreId,
    customer_id: 0,
    amount: 0,
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: '',
    description: '',
  };

  // 重置明细项
  invoiceItems.value = [];
  itemForm.value = {
    item_name: '',
    item_description: '',
    quantity: 1,
    unit_price: 0,
  };

  // 重置附件状态
  resetAttachments();

  formVisible.value = true;
};

// 附件上传成功处理
const handleAttachmentSuccess = (attachment: any) => {
  attachments.value.push(attachment);
  message.success(`附件 "${attachment.original_name}" 上传成功`);
};

// 附件上传错误处理
const handleAttachmentError = (error: string) => {
  message.error(`附件上传失败: ${error}`);
};

// 重置附件状态
const resetAttachments = () => {
  attachments.value = [];
  tempFiles.value = [];
  attachmentUploadKey.value += 1; // 强制重新渲染附件组件
};

// 临时文件选择处理
const handleTempFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    const newFiles = Array.from(target.files);
    tempFiles.value = [...tempFiles.value, ...newFiles];
    message.success(`已选择 ${newFiles.length} 个文件`);
  }
};

// 移除临时文件
const removeTempFile = (index: number) => {
  tempFiles.value.splice(index, 1);
};

// 批量上传临时文件
const uploadTempFiles = async (invoiceId: number) => {
  if (tempFiles.value.length === 0) return;

  uploadingFiles.value = true;
  const uploadPromises = tempFiles.value.map(async (file) => {
    try {
      const { uploadFileApi } = await import('#/api/modules/attachment');
      return await uploadFileApi(file, 'invoice', invoiceId);
    } catch (error) {
      console.error(`文件 ${file.name} 上传失败:`, error);
      throw error;
    }
  });

  try {
    const uploadedAttachments = await Promise.all(uploadPromises);
    attachments.value = [...attachments.value, ...uploadedAttachments];
    tempFiles.value = []; // 清空临时文件
    message.success(`成功上传 ${uploadedAttachments.length} 个附件`);
  } catch (error) {
    message.error('部分文件上传失败，请重试');
    throw error;
  } finally {
    uploadingFiles.value = false;
  }
};

// 显示编辑表单
const showEditForm = (invoice: InvoiceApi.Invoice) => {
  editingInvoice.value = invoice;
  createMode.value = 'traditional'; // 编辑时使用传统模式
  invoiceForm.value = {
    store_id: invoice.store_id,
    customer_id: invoice.customer_id,
    amount: parseFloat(invoice.amount),
    invoice_date: invoice.invoice_date,
    due_date: invoice.due_date,
    description: invoice.description || '',
  };
  formVisible.value = true;
};

// 监听创建模式变化
watch(createMode, (newMode, oldMode) => {
  console.log('创建模式变化:', { oldMode, newMode });

  if (newMode === 'detailed') {
    // 切换到明细模式时，清空金额字段
    invoiceForm.value.amount = 0;
    console.log('切换到明细模式，清空金额字段');
  } else {
    // 切换到传统模式时，清空明细项
    invoiceItems.value = [];
    console.log('切换到传统模式，清空明细项');
  }
});

// 添加明细项
const addInvoiceItem = () => {
  console.log('添加明细项，当前表单数据:', itemForm.value);

  if (itemForm.value.quantity <= 0 || itemForm.value.unit_price <= 0) {
    console.log('验证失败:', {
      item_name: itemForm.value.item_name,
      quantity: itemForm.value.quantity,
      unit_price: itemForm.value.unit_price
    });
    message.error('请填写有效的数量和单价');
    return;
  }

  const newItem = {
    ...itemForm.value,
    sort_order: invoiceItems.value.length + 1,
  };

  console.log('添加新明细项:', newItem);
  invoiceItems.value.push(newItem);
  console.log('当前明细项列表:', invoiceItems.value);

  // 重置表单
  itemForm.value = {
    item_name: '',
    item_description: '',
    quantity: 1,
    unit_price: 0,
  };

  console.log('表单已重置');
};

// 删除明细项
const removeInvoiceItem = (index: number) => {
  invoiceItems.value.splice(index, 1);
  // 重新排序
  invoiceItems.value.forEach((item, idx) => {
    item.sort_order = idx + 1;
  });
};

// 提交表单
const handleSubmit = async () => {
  try {
    formLoading.value = true;

    if (editingInvoice.value) {
      // 编辑账单（只支持传统模式）
      await updateInvoiceApi(editingInvoice.value.id, invoiceForm.value);
      message.success('账单信息更新成功');
    } else {
      // 新增账单
      let submitData: InvoiceApi.InvoiceFormData;

      if (createMode.value === 'detailed') {
        // 明细模式：验证明细项
        if (invoiceItems.value.length === 0) {
          message.error('明细模式下至少需要添加一个明细项');
          return;
        }

        submitData = {
          ...invoiceForm.value,
          items: invoiceItems.value,
        };
        // 明细模式下不传递amount字段
        delete submitData.amount;
      } else {
        // 传统模式
        if (!invoiceForm.value.amount || invoiceForm.value.amount <= 0) {
          message.error('请输入有效的账单金额');
          return;
        }

        submitData = { ...invoiceForm.value };
      }

      const createdInvoice = await createInvoiceApi(submitData);

      // 如果有临时文件，上传它们
      if (tempFiles.value.length > 0) {
        try {
          await uploadTempFiles(createdInvoice.id);
          message.success(`账单创建成功，已上传 ${tempFiles.value.length} 个附件`);
        } catch (uploadError) {
          message.warning('账单创建成功，但部分附件上传失败，请在编辑页面重新上传');
        }
      } else {
        message.success('账单创建成功');
      }
    }

    formVisible.value = false;
    fetchInvoices();
  } catch (error) {
    console.error('保存账单失败:', error);
    message.error('保存账单失败');
  } finally {
    formLoading.value = false;
  }
};

// 删除账单
const handleDelete = async (invoice: InvoiceApi.Invoice) => {
  try {
    await deleteInvoiceApi(invoice.id);
    message.success('账单删除成功');
    fetchInvoices();
  } catch (error) {
    console.error('删除账单失败:', error);
  }
};

// 查看账单详情
const viewInvoiceDetail = (invoice: InvoiceApi.Invoice) => {
  router.push(`/invoice/detail/${invoice.id}`);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchInvoices();
  fetchCustomers();
  fetchStores();
});
</script>

<template>
  <div class="p-4">
    <Card title="账单管理">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <Space wrap>
          <Select
            v-model:value="searchForm.customer_id"
            placeholder="选择客户"
            style="width: 200px"
            allow-clear
            show-search
            :filter-option="(input: string, option: any) => 
              option.label.toLowerCase().includes(input.toLowerCase())
            "
          >
            <Select.Option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
              :label="customer.name"
            >
              {{ customer.name }}
            </Select.Option>
          </Select>
          <Select
            v-model:value="searchForm.status"
            placeholder="选择状态"
            style="width: 120px"
            allow-clear
          >
            <Select.Option value="unpaid">未付款</Select.Option>
            <Select.Option value="partially_paid">部分付款</Select.Option>
            <Select.Option value="paid">已付清</Select.Option>
            <Select.Option value="overdue">已逾期</Select.Option>
          </Select>
          <DatePicker
            v-model:value="searchForm.start_date"
            placeholder="开始日期"
            value-format="YYYY-MM-DD"
          />
          <DatePicker
            v-model:value="searchForm.end_date"
            placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
          <Button type="primary" @click="handleSearch">
            <AntDesignSearchOutlined />
            搜索
          </Button>
          <Button @click="handleReset">重置</Button>
          <Button type="primary" @click="showAddForm">
            <AntDesignPlusOutlined />
            新增账单
          </Button>
        </Space>
      </div>

      <!-- 账单列表表格 -->
      <Table
        :columns="columns"
        :data-source="invoices"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1200 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <Space>
              <Button type="link" size="small" @click="viewInvoiceDetail(record)">
                <AntDesignEyeOutlined />
                详情
              </Button>
              <Button type="link" size="small" @click="showEditForm(record)">
                <AntDesignEditOutlined />
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个账单吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <Button type="link" size="small" danger>
                  <AntDesignDeleteOutlined />
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 账单表单弹窗 -->
    <Modal
      v-model:open="formVisible"
      :title="editingInvoice ? '编辑账单' : '新增账单'"
      :confirm-loading="formLoading"
      width="600px"
      @ok="handleSubmit"
      @cancel="formVisible = false"
    >
      <Form :model="invoiceForm" layout="vertical">
        <!-- 门店选择：管理员或多门店用户显示选择框 -->
        <Form.Item
          v-if="showStoreSelection"
          label="门店"
          name="store_id"
          :rules="[{ required: true, message: '请选择门店' }]"
        >
          <Select
            v-model:value="invoiceForm.store_id"
            placeholder="请选择门店"
            show-search
            :filter-option="(input: string, option: any) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            "
          >
            <Select.Option
              v-for="store in availableStores"
              :key="store.id"
              :value="store.id"
              :label="store.name"
            >
              {{ store.name }}
            </Select.Option>
          </Select>
        </Form.Item>

        <!-- 单门店用户显示固定门店信息 -->
        <div v-else-if="userStores.length === 1" class="mb-4">
          <label class="text-sm font-medium text-muted-foreground">当前门店</label>
          <div class="mt-1 p-2 bg-accent/20 border border-border rounded text-foreground">
            {{ userStores[0]?.name || '未分配门店' }}
          </div>
        </div>

        <!-- 无门店权限提示 -->
        <div v-else class="mb-4">
          <label class="text-sm font-medium text-destructive">门店权限</label>
          <div class="mt-1 p-2 bg-destructive/10 border border-destructive/20 rounded text-destructive">
            您尚未分配任何门店权限，请联系管理员
          </div>
        </div>

        <!-- 创建模式选择（仅新增时显示） -->
        <Form.Item v-if="!editingInvoice" label="创建模式">
          <Radio.Group v-model:value="createMode">
            <Radio value="traditional">传统模式（直接输入金额）</Radio>
            <Radio value="detailed">明细模式（添加商品明细）</Radio>
          </Radio.Group>
          <!-- 调试信息 -->
          <div class="text-sm text-gray-500 mt-2">
            当前模式: {{ createMode }}
          </div>
        </Form.Item>

        <Form.Item label="客户" name="customer_id" :rules="[{ required: true, message: '请选择客户' }]">
          <Select
            v-model:value="invoiceForm.customer_id"
            placeholder="请选择客户"
            show-search
            :filter-option="(input: string, option: any) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            "
          >
            <Select.Option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
              :label="customer.name"
            >
              {{ customer.name }}
            </Select.Option>
          </Select>
        </Form.Item>
        <!-- 传统模式：金额输入 -->
        <Form.Item
          v-if="createMode === 'traditional'"
          label="账单金额"
          name="amount"
          :rules="[{ required: true, message: '请输入账单金额' }]"
        >
          <Input
            v-model:value="invoiceForm.amount"
            type="number"
            placeholder="请输入账单金额"
            addon-before="¥"
          />
        </Form.Item>

        <!-- 明细模式：明细项管理 -->
        <div v-if="createMode === 'detailed'">
          <Form.Item label="商品明细">
            <!-- 明细项添加表单 -->
            <Card
              title="添加商品明细"
              size="small"
              class="mb-6 shadow-sm border-border bg-accent/5 dark:bg-accent/10"
            >
              <template #extra>
                <div class="flex items-center text-sm text-muted-foreground">
                  <span class="mr-2">📦</span>
                  <span>填写商品信息</span>
                </div>
              </template>

              <div class="space-y-4">
                <!-- 第一行：商品名称和描述 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      商品名称
                    </label>
                    <Input
                      v-model:value="itemForm.item_name"
                      placeholder="商品名称（可选）"
                      size="large"
                      class="rounded-lg"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      商品描述
                    </label>
                    <Input
                      v-model:value="itemForm.item_description"
                      placeholder="商品描述（可选）"
                      size="large"
                      class="rounded-lg"
                    />
                  </div>
                </div>

                <!-- 第二行：数量、单价和小计 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      <span class="text-destructive">*</span> 数量
                    </label>
                    <Input
                      v-model:value="itemForm.quantity"
                      type="number"
                      placeholder="请输入数量"
                      size="large"
                      min="0.001"
                      step="0.001"
                      class="rounded-lg"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      <span class="text-destructive">*</span> 单价
                    </label>
                    <Input
                      v-model:value="itemForm.unit_price"
                      type="number"
                      placeholder="请输入单价"
                      size="large"
                      addon-before="¥"
                      min="0.01"
                      step="0.01"
                      class="rounded-lg"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      小计金额
                    </label>
                    <div class="h-10 flex items-center px-3 bg-muted border border-border rounded-lg">
                      <span class="text-lg font-semibold text-primary">
                        ¥{{ (itemForm.quantity * itemForm.unit_price || 0).toFixed(2) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 添加按钮 -->
                <div class="flex justify-end pt-2">
                  <Button
                    type="primary"
                    @click="addInvoiceItem"
                    size="large"
                    class="px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    <AntDesignPlusOutlined class="mr-1" />
                    添加到明细
                  </Button>
                </div>
              </div>
            </Card>

            <!-- 明细项列表 -->
            <Card
              v-if="invoiceItems.length > 0"
              title="商品明细列表"
              size="small"
              class="mb-4 shadow-sm border-border"
            >
              <template #extra>
                <div class="flex items-center text-sm text-muted-foreground">
                  <span class="mr-2">📋</span>
                  <span>共 {{ invoiceItems.length }} 项商品</span>
                </div>
              </template>

              <div class="overflow-x-auto">
                <table class="w-full">
                  <thead>
                    <tr class="border-b border-border bg-muted/50">
                      <th class="text-left py-3 px-4 font-medium text-foreground">商品名称</th>
                      <th class="text-left py-3 px-4 font-medium text-foreground">描述</th>
                      <th class="text-center py-3 px-4 font-medium text-foreground">数量</th>
                      <th class="text-center py-3 px-4 font-medium text-foreground">单价</th>
                      <th class="text-center py-3 px-4 font-medium text-foreground">小计</th>
                      <th class="text-center py-3 px-4 font-medium text-foreground">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(item, index) in invoiceItems"
                      :key="index"
                      class="border-b border-border/50 hover:bg-accent/5 transition-colors duration-150"
                    >
                      <td class="py-3 px-4">
                        <div class="font-medium text-foreground">{{ item.item_name }}</div>
                      </td>
                      <td class="py-3 px-4">
                        <div class="text-muted-foreground">{{ item.item_description || '-' }}</div>
                      </td>
                      <td class="py-3 px-4 text-center">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-sm bg-primary/10 text-primary dark:bg-primary/20">
                          {{ item.quantity }}
                        </span>
                      </td>
                      <td class="py-3 px-4 text-center">
                        <span class="font-medium text-foreground">¥{{ item.unit_price }}</span>
                      </td>
                      <td class="py-3 px-4 text-center">
                        <span class="font-semibold text-green-600 dark:text-green-400">
                          ¥{{ (item.quantity * item.unit_price).toFixed(2) }}
                        </span>
                      </td>
                      <td class="py-3 px-4 text-center">
                        <Button
                          type="text"
                          danger
                          size="small"
                          @click="removeInvoiceItem(index)"
                          class="hover:bg-destructive/10 rounded-lg transition-colors duration-150"
                        >
                          <AntDesignDeleteOutlined class="text-base" />
                        </Button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </Card>

            <!-- 空状态提示 -->
            <Card
              v-else
              size="small"
              class="mb-4 border-dashed border-2 border-border bg-muted/30"
            >
              <div class="text-center py-8">
                <div class="text-4xl mb-3">📦</div>
                <div class="text-muted-foreground mb-2">暂无商品明细</div>
                <div class="text-sm text-muted-foreground/70">请在上方添加商品明细项</div>
              </div>
            </Card>

            <!-- 总金额显示 -->
            <Card
              v-if="invoiceItems.length > 0"
              size="small"
              class="bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800 shadow-sm"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <span class="text-2xl mr-3">💰</span>
                  <div>
                    <div class="text-sm text-foreground/80">账单总金额</div>
                    <div class="text-xs text-muted-foreground">共 {{ invoiceItems.length }} 项商品</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                    ¥{{ totalAmount.toFixed(2) }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    平均单价 ¥{{ (totalAmount / invoiceItems.reduce((sum, item) => sum + item.quantity, 0)).toFixed(2) }}
                  </div>
                </div>
              </div>
            </Card>
          </Form.Item>
        </div>
        <Form.Item label="账单日期" name="invoice_date" :rules="[{ required: true, message: '请选择账单日期' }]">
          <DatePicker
            v-model:value="invoiceForm.invoice_date"
            placeholder="请选择账单日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </Form.Item>
        <Form.Item label="到期日期" name="due_date">
          <DatePicker
            v-model:value="invoiceForm.due_date"
            placeholder="请选择到期日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </Form.Item>
        <Form.Item label="账单描述" name="description">
          <Input.TextArea
            v-model:value="invoiceForm.description"
            placeholder="请输入账单描述"
            :rows="3"
          />
        </Form.Item>

        <!-- 附件上传 -->
        <Form.Item label="相关附件">
          <div class="space-y-4">
            <!-- 功能说明 -->
            <div class="flex items-start space-x-2 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div class="text-blue-500 mt-0.5 text-lg">
                ℹ️
              </div>
              <div class="text-sm text-blue-700 dark:text-blue-300">
                <div class="font-medium mb-1">支持的文件类型</div>
                <div class="text-xs space-y-1">
                  <div>• 文档：PDF、Word、Excel 文件</div>
                  <div>• 图片：JPG、PNG 格式</div>
                  <div>• 大小限制：单个文件最大 10MB</div>
                </div>
              </div>
            </div>

            <!-- 编辑模式：使用AttachmentUpload组件 -->
            <div v-if="editingInvoice" class="border border-border rounded-lg p-4 bg-gradient-to-br from-background to-accent/5">
              <AttachmentUpload
                :key="attachmentUploadKey"
                :attachable-type="'invoice'"
                :attachable-id="editingInvoice.id"
                :multiple="true"
                @success="handleAttachmentSuccess"
                @error="handleAttachmentError"
              />
            </div>

            <!-- 新增模式：文件选择器 -->
            <div v-else class="border border-dashed border-border rounded-lg p-6 bg-gradient-to-br from-background to-accent/5">
              <div class="text-center space-y-4">
                <!-- 上传图标 -->
                <div class="flex justify-center">
                  <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <span class="text-3xl">☁️</span>
                  </div>
                </div>

                <!-- 上传按钮和说明 -->
                <div class="space-y-2">
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx"
                    @change="handleTempFileSelect"
                    style="display: none !important; position: absolute; left: -9999px; width: 0; height: 0; opacity: 0;"
                    ref="fileInput"
                  />
                  <Button
                    type="primary"
                    size="large"
                    @click="fileInput?.click()"
                    :loading="uploadingFiles"
                    class="shadow-sm"
                  >
                    <AntDesignPlusOutlined class="mr-2" />
                    选择文件
                  </Button>
                  <div class="text-xs text-muted-foreground">
                    点击选择文件或拖拽文件到此区域
                  </div>
                </div>
              </div>

              <!-- 已选择的临时文件列表 -->
              <div v-if="tempFiles.length > 0" class="mt-6 pt-4 border-t border-border">
                <div class="flex items-center justify-between mb-3">
                  <div class="text-sm font-medium text-foreground">已选择文件 ({{ tempFiles.length }})</div>
                  <Button type="text" size="small" @click="tempFiles = []" class="text-muted-foreground hover:text-foreground">
                    清空全部
                  </Button>
                </div>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                  <div
                    v-for="(file, index) in tempFiles"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-background border border-border rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div class="flex items-center space-x-3 flex-1 min-w-0">
                      <div class="text-primary">
                        📄
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-sm font-medium text-foreground truncate">{{ file.name }}</div>
                        <div class="text-xs text-muted-foreground">{{ (file.size / 1024 / 1024).toFixed(2) }} MB</div>
                      </div>
                    </div>
                    <Button
                      type="text"
                      size="small"
                      danger
                      @click="removeTempFile(index)"
                      class="flex-shrink-0 ml-2"
                    >
                      <AntDesignDeleteOutlined />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已上传附件列表 -->
            <div v-if="attachments.length > 0" class="border border-border rounded-lg p-4 bg-green-50 dark:bg-green-950/20">
              <div class="flex items-center space-x-2 mb-3">
                <div class="text-green-600 dark:text-green-400 text-lg">
                  ✅
                </div>
                <div class="text-sm font-medium text-green-700 dark:text-green-300">已上传附件 ({{ attachments.length }})</div>
              </div>
              <div class="space-y-2">
                <div
                  v-for="attachment in attachments"
                  :key="attachment.id"
                  class="flex items-center justify-between p-3 bg-background border border-border rounded-lg"
                >
                  <div class="flex items-center space-x-3">
                    <div class="text-green-600">
                      📄
                    </div>
                    <div>
                      <div class="text-sm font-medium text-foreground">{{ attachment.original_name }}</div>
                      <div class="text-xs text-muted-foreground">{{ attachment.file_size }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
