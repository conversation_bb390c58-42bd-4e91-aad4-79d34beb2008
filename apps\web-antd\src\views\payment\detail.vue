<script setup lang="ts">
import { onMounted, ref, computed, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Card, Descriptions, Table, Tag, Button, Statistic, Row, Col, message } from 'ant-design-vue';
import AttachmentManager from '#/components/AttachmentManager.vue';
import { AntDesignArrowLeftOutlined } from '@vben/icons';
import dayjs from 'dayjs';

import {
  getPaymentDetailApi,
  type PaymentApi,
} from '#/api/modules/payment';

const route = useRoute();
const router = useRouter();

// 获取还款ID
const paymentId = Number(route.params.id);

// 响应式数据
const loading = ref(false);
const payment = ref<PaymentApi.PaymentDetail | null>(null);

// 支付方式映射
const paymentMethodMap = {
  cash: { text: '现金', color: 'green' },
  bank_transfer: { text: '银行转账', color: 'blue' },
  wechat: { text: '微信支付', color: 'green' },
  alipay: { text: '支付宝', color: 'blue' },
  other: { text: '其他', color: 'default' },
};

// 分配记录列定义
const allocationColumns = [
  {
    title: '账单编号',
    dataIndex: ['invoice', 'invoice_number'],
    key: 'invoice_number',
    width: 200,
  },
  {
    title: '分配金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '账单总额',
    dataIndex: ['invoice', 'amount'],
    key: 'invoice_amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '已付金额',
    dataIndex: ['invoice', 'paid_amount'],
    key: 'paid_amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '账单状态',
    dataIndex: ['invoice', 'status'],
    key: 'status',
    width: 100,
    customRender: ({ text }: { text: string }) => {
      const statusMap = {
        unpaid: { text: '未付款', color: 'red' },
        partially_paid: { text: '部分付款', color: 'orange' },
        paid: { text: '已付款', color: 'green' },
        overdue: { text: '逾期', color: 'red' },
      };
      const status = statusMap[text as keyof typeof statusMap] || { text, color: 'default' };
      return h(Tag, { color: status.color }, () => status.text);
    },
  },
  {
    title: '分配时间',
    dataIndex: 'allocated_at',
    key: 'allocated_at',
    width: 150,
    customRender: ({ text }: { text: string }) => dayjs(text).format('YYYY-MM-DD HH:mm'),
  },
  {
    title: '分配人',
    dataIndex: ['allocated_by', 'name'],
    key: 'allocated_by',
    width: 100,
  },
];

// 计算剩余未分配金额
const unallocatedAmount = computed(() => {
  if (!payment.value) return 0;
  return parseFloat(payment.value.amount) - parseFloat(payment.value.allocated_amount);
});

// 获取还款详情
const fetchPaymentDetail = async () => {
  try {
    loading.value = true;
    const detailResponse = await getPaymentDetailApi(paymentId);
    payment.value = detailResponse;
  } catch (error) {
    console.error('获取还款详情失败:', error);
    message.error('获取还款详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回还款列表
const goBack = () => {
  router.push('/payment');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPaymentDetail();
});
</script>

<template>
  <div class="p-4">
    <!-- 页面头部 -->
    <div class="mb-4">
      <Button @click="goBack">
        <AntDesignArrowLeftOutlined />
        返回还款列表
      </Button>
    </div>

    <div v-if="payment" class="space-y-4">
      <!-- 还款基本信息 -->
      <Card title="还款基本信息" :loading="loading">
        <Row :gutter="16" class="mb-4">
          <Col :span="6">
            <Statistic
              title="还款金额"
              :value="payment.amount"
              prefix="¥"
              :value-style="{ color: '#1890ff' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="已分配金额"
              :value="payment.allocated_amount"
              prefix="¥"
              :value-style="{ color: '#52c41a' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="未分配金额"
              :value="unallocatedAmount.toFixed(2)"
              prefix="¥"
              :value-style="{ color: unallocatedAmount > 0 ? '#f5222d' : '#52c41a' }"
            />
          </Col>
          <Col :span="6">
            <div class="text-center">
              <div class="text-muted-foreground text-sm mb-1">支付方式</div>
              <Tag :color="paymentMethodMap[payment.payment_method]?.color || 'default'" class="text-base px-3 py-1">
                {{ paymentMethodMap[payment.payment_method]?.text || payment.payment_method }}
              </Tag>
            </div>
          </Col>
        </Row>

        <Descriptions :column="2" bordered>
          <Descriptions.Item label="还款编号">{{ payment.payment_number }}</Descriptions.Item>
          <Descriptions.Item label="客户姓名">{{ payment.customer?.name || '-' }}</Descriptions.Item>
          <Descriptions.Item label="客户电话">{{ payment.customer?.phone || '-' }}</Descriptions.Item>
          <Descriptions.Item label="所属门店">{{ payment.store?.name || '-' }}</Descriptions.Item>
          <Descriptions.Item label="还款日期">{{ payment.payment_date }}</Descriptions.Item>
          <Descriptions.Item label="收款人">{{ payment.received_by?.name || '-' }}</Descriptions.Item>
          <Descriptions.Item label="参考号码">{{ payment.reference_number || '-' }}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{{ payment.created_at ? dayjs(payment.created_at).format('YYYY-MM-DD HH:mm:ss') : '-' }}</Descriptions.Item>
          <Descriptions.Item label="备注" :span="2">{{ payment.remarks || '-' }}</Descriptions.Item>
        </Descriptions>
      </Card>

      <!-- 分配记录 -->
      <Card title="分配记录">
        <Table
          :columns="allocationColumns"
          :data-source="payment.allocations"
          :pagination="false"
          :scroll="{ x: 900 }"
          row-key="id"
        >
          <template #emptyText>
            <div class="text-center py-8">
              <p>暂无分配记录</p>
              <Button type="primary" @click="$router.push('/payment')">
                分配还款
              </Button>
            </div>
          </template>
        </Table>
      </Card>

      <!-- 附件管理 -->
      <AttachmentManager
        attachable-type="payment"
        :attachable-id="paymentId"
        title="还款附件"
      />
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="text-center py-8">
      <div>加载中...</div>
    </div>

    <!-- 数据为空 -->
    <div v-else class="text-center py-8">
      <div>还款详情不存在</div>
      <Button type="primary" @click="goBack" class="mt-4">
        返回还款列表
      </Button>
    </div>
  </div>
</template>
