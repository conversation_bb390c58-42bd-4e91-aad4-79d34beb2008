<script lang="ts" setup>
import { onMounted, ref, reactive, h, computed } from 'vue';
import { Card, Button, Input, Table, Space, Modal, Form, message, Popconfirm, Select, DatePicker, Tag } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { AntDesignSearchOutlined, AntDesignPlusOutlined, AntDesignEditOutlined, AntDesignDeleteOutlined, AntDesignEyeOutlined } from '@vben/icons';
import dayjs from 'dayjs';

import {
  getPaymentListApi,
  createPaymentApi,
  deletePaymentApi,
  type PaymentApi,
} from '#/api/modules/payment';

import { getCustomerListApi, type CustomerApi } from '#/api/modules/customer';
import { getInvoiceListApi, type InvoiceApi } from '#/api/modules/invoice';
import { getStoreListApi, type StoreApi } from '#/api/modules/store';
import { useUserStore } from '@vben/stores';

const router = useRouter();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const payments = ref<PaymentApi.Payment[]>([]);
const customers = ref<CustomerApi.Customer[]>([]);
const stores = ref<StoreApi.Store[]>([]);
const customerInvoices = ref<InvoiceApi.Invoice[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 用户角色检查
const isAdmin = computed(() => {
  return userStore.userRoles.includes('admin');
});

const userStores = computed(() => {
  return userStore.userInfo?.stores || [];
});

// 是否显示门店选择（管理员或多门店用户显示选择框）
const showStoreSelection = computed(() => {
  return isAdmin.value || userStores.value.length > 1;
});

// 获取可选择的门店列表
const availableStores = computed(() => {
  if (isAdmin.value) {
    // 管理员可以选择所有门店
    return stores.value;
  } else {
    // 普通用户只能选择其所属门店
    return userStores.value;
  }
});



// 搜索表单
const searchForm = reactive({
  customer_id: undefined,
  payment_method: undefined,
  start_date: undefined,
  end_date: undefined,
});

// 还款表单
const paymentForm = ref<PaymentApi.PaymentFormData>({
  store_id: 0,
  customer_id: 0,
  amount: 0,
  payment_date: '',
  payment_method: 'cash',
  reference_number: '',
  remarks: '',
  allocations: [],
});

// 表单状态
const formVisible = ref(false);
const formLoading = ref(false);
const autoAllocateLoading = ref(false);

// 附件相关状态
const attachments = ref<any[]>([]);
const tempFiles = ref<File[]>([]); // 临时存储的文件
const uploadingFiles = ref(false); // 文件上传状态
const fileInput = ref<HTMLInputElement>(); // 文件输入引用

// 减免类型选项（保留给分配项使用）
const discountTypes = [
  { value: 'discount', label: '折扣优惠' },
  { value: 'promotion', label: '促销优惠' },
  { value: 'bad_debt', label: '坏账核销' },
];

// 计算分配总额
const totalAllocated = computed(() => {
  return paymentForm.value.allocations?.reduce((sum, allocation) => sum + (Number(allocation.amount) || 0), 0) || 0;
});

// 计算客户所有未付账单的总未付金额（用于验证）
const totalUnpaidAmount = computed(() => {
  return customerInvoices.value.reduce((sum, invoice) => {
    const unpaidAmount = parseFloat(invoice.amount) - parseFloat(invoice.paid_amount);
    return sum + Math.max(0, unpaidAmount); // 确保不为负数
  }, 0);
});

// 计算已分配账单的总未付金额（新的业务逻辑）
const allocatedInvoicesUnpaidAmount = computed(() => {
  if (!paymentForm.value.allocations || paymentForm.value.allocations.length === 0) {
    return 0;
  }

  return paymentForm.value.allocations.reduce((sum, allocation) => {
    const invoice = customerInvoices.value.find(inv => inv.id === allocation.invoice_id);
    if (invoice) {
      const unpaidAmount = parseFloat(invoice.amount) - parseFloat(invoice.paid_amount);
      return sum + Math.max(0, unpaidAmount);
    }
    return sum;
  }, 0);
});

// 注意：全局优惠减免逻辑已移除，现在使用分配项级别的优惠减免

// 支付方式选项
const paymentMethods = [
  { value: 'cash', label: '现金' },
  { value: 'bank_transfer', label: '银行转账' },
  { value: 'wechat', label: '微信支付' },
  { value: 'alipay', label: '支付宝' },
  { value: 'other', label: '其他' },
];

// 支付方式映射
const paymentMethodMap = {
  cash: '现金',
  bank_transfer: '银行转账',
  wechat: '微信支付',
  alipay: '支付宝',
  other: '其他',
};

// 表格列定义
const columns = [
  {
    title: '还款编号',
    dataIndex: 'payment_number',
    key: 'payment_number',
    width: 180,
  },
  {
    title: '客户姓名',
    dataIndex: ['customer', 'name'],
    key: 'customer_name',
    width: 120,
  },
  {
    title: '还款金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '已分配金额',
    dataIndex: 'allocated_amount',
    key: 'allocated_amount',
    width: 120,
    customRender: ({ text }: { text: string }) => `¥${text}`,
  },
  {
    title: '还款方式',
    dataIndex: 'payment_method',
    key: 'payment_method',
    width: 120,
    customRender: ({ text }: { text: keyof typeof paymentMethodMap }) => {
      return paymentMethodMap[text] || text;
    },
  },
  {
    title: '还款日期',
    dataIndex: 'payment_date',
    key: 'payment_date',
    width: 120,
  },
  {
    title: '收款人',
    dataIndex: ['received_by_user', 'name'],
    key: 'received_by_name',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160,
    customRender: ({ text }: { text: string }) => {
      return new Date(text).toLocaleString();
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right' as const,
  },
];

// 获取还款列表
const fetchPayments = async () => {
  try {
    loading.value = true;
    const params: PaymentApi.PaymentListParams = {
      page: pagination.current,
      per_page: pagination.pageSize,
    };
    
    if (searchForm.customer_id) {
      params.customer_id = searchForm.customer_id;
    }
    if (searchForm.payment_method) {
      params.payment_method = searchForm.payment_method;
    }
    if (searchForm.start_date) {
      params.start_date = searchForm.start_date;
    }
    if (searchForm.end_date) {
      params.end_date = searchForm.end_date;
    }
    
    const response = await getPaymentListApi(params);
    payments.value = response.data;
    pagination.total = response.total;
    pagination.current = response.current_page;
  } catch (error) {
    console.error('获取还款列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取客户列表
const fetchCustomers = async () => {
  try {
    const response = await getCustomerListApi({ per_page: 1000 });
    customers.value = response.data;
  } catch (error) {
    console.error('获取客户列表失败:', error);
  }
};

// 获取门店列表（仅管理员需要获取所有门店）
const fetchStores = async () => {
  if (!isAdmin.value) {
    // 普通用户使用已有的门店信息，无需额外获取
    return;
  }

  try {
    const response = await getStoreListApi();
    stores.value = response;
  } catch (error) {
    console.error('获取门店列表失败:', error);
  }
};

// 获取客户的未付清账单
const fetchCustomerInvoices = async (customerId: number) => {
  try {
    // 修复：API不支持多个status值，需要分别查询
    const allInvoices: any[] = [];

    // 分别查询未付和部分付款的账单
    const statuses = ['unpaid', 'partially_paid'];

    for (const status of statuses) {
      const params: any = {
        customer_id: customerId,
        status: status,
        per_page: 1000
      };

      if (paymentForm.value.store_id) {
        params.store_id = paymentForm.value.store_id;
      }

      const response = await getInvoiceListApi(params);

      if (response && typeof response === 'object' && 'data' in response) {
        allInvoices.push(...(response as any).data || []);
      } else {
        allInvoices.push(...(Array.isArray(response) ? response : []));
      }
    }

    // 去重（防止重复账单）
    const uniqueInvoices = allInvoices.filter((invoice, index, self) =>
      index === self.findIndex(i => i.id === invoice.id)
    );

    customerInvoices.value = uniqueInvoices;
  } catch (error) {
    console.error('获取客户账单失败:', error);
    customerInvoices.value = [];
  }
};

// 搜索还款
const handleSearch = () => {
  pagination.current = 1;
  fetchPayments();
};

// 重置搜索
const handleReset = () => {
  searchForm.customer_id = undefined;
  searchForm.payment_method = undefined;
  searchForm.start_date = undefined;
  searchForm.end_date = undefined;
  pagination.current = 1;
  fetchPayments();
};

// 分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchPayments();
};

// 显示新增表单
const showAddForm = () => {
  // 根据用户门店情况设置默认门店
  let defaultStoreId = 0;
  if (!isAdmin.value && userStores.value.length === 1) {
    // 单门店用户自动设置为唯一门店
    defaultStoreId = userStores.value[0].id;
  }
  // 管理员和多门店用户不设置默认值，要求用户选择

  paymentForm.value = {
    store_id: defaultStoreId,
    customer_id: 0,
    amount: 0,
    payment_date: dayjs().format('YYYY-MM-DD'),
    payment_method: 'cash',
    reference_number: '',
    remarks: '',
    allocations: [],
  };

  // 重置分配项的优惠减免状态
  // 新的分配项会在 addAllocation 函数中自动初始化

  customerInvoices.value = [];

  // 重置附件状态
  resetAttachments();

  formVisible.value = true;
};

// 重置附件状态
const resetAttachments = () => {
  attachments.value = [];
  tempFiles.value = [];
};

// 临时文件选择处理
const handleTempFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    const newFiles = Array.from(target.files);
    tempFiles.value = [...tempFiles.value, ...newFiles];
    message.success(`已选择 ${newFiles.length} 个文件`);
  }
};

// 移除临时文件
const removeTempFile = (index: number) => {
  tempFiles.value.splice(index, 1);
};

// 批量上传临时文件
const uploadTempFiles = async (paymentId: number) => {
  if (tempFiles.value.length === 0) return;

  uploadingFiles.value = true;
  const uploadPromises = tempFiles.value.map(async (file) => {
    try {
      const { uploadFileApi } = await import('#/api/modules/attachment');
      return await uploadFileApi(file, 'payment', paymentId);
    } catch (error) {
      console.error(`文件 ${file.name} 上传失败:`, error);
      throw error;
    }
  });

  try {
    const uploadedAttachments = await Promise.all(uploadPromises);
    attachments.value = [...attachments.value, ...uploadedAttachments];
    tempFiles.value = []; // 清空临时文件
    message.success(`成功上传 ${uploadedAttachments.length} 个附件`);
  } catch (error) {
    message.error('部分文件上传失败，请重试');
    throw error;
  } finally {
    uploadingFiles.value = false;
  }
};

// 客户变化时获取其账单
const handleCustomerChange = (customerId: number) => {
  if (customerId) {
    fetchCustomerInvoices(customerId);
  } else {
    customerInvoices.value = [];
  }
  paymentForm.value.allocations = [];
};

// 门店变化时重新获取客户账单
const handleStoreChange = (storeId: number) => {
  // 如果已经选择了客户，重新获取该客户在新门店的账单
  if (paymentForm.value.customer_id) {
    fetchCustomerInvoices(paymentForm.value.customer_id);
  }
  paymentForm.value.allocations = [];
};

// 扩展的分配项接口（前端使用）
interface ExtendedAllocation {
  invoice_id: number;
  amount: number;
  discount?: {
    type: 'discount' | 'promotion' | 'bad_debt';
    amount: number;
    reason: string;
    enabled: boolean; // 是否启用优惠减免
  };
}

// 确保分配项具有完整的数据结构
const ensureAllocationStructure = (allocation: any): ExtendedAllocation => {
  if (!allocation.discount) {
    allocation.discount = {
      type: 'discount',
      amount: 0,
      reason: '',
      enabled: false,
    };
  }
  return allocation as ExtendedAllocation;
};

// 计算优惠减免总额和相关信息
const calculateDiscountInfo = () => {
  let totalAmount = 0;
  const reasons: string[] = [];

  if (paymentForm.value.allocations) {
    for (let i = 0; i < paymentForm.value.allocations.length; i++) {
      const allocation = paymentForm.value.allocations[i] as ExtendedAllocation;
      if (allocation.discount?.enabled && allocation.discount.amount > 0) {
        totalAmount += allocation.discount.amount;
        reasons.push(`${allocation.discount.reason} (¥${allocation.discount.amount.toFixed(2)})`);
      }
    }
  }

  return { totalAmount, reasons };
};

// 获取指定分配项可选的账单列表（过滤已分配的账单）
const getAvailableInvoicesForAllocation = (currentIndex: number) => {
  if (!paymentForm.value.allocations) {
    return customerInvoices.value;
  }

  // 获取其他分配项已选择的账单ID
  const allocatedInvoiceIds = new Set<number>();
  paymentForm.value.allocations.forEach((allocation, index) => {
    if (index !== currentIndex && allocation.invoice_id > 0) {
      allocatedInvoiceIds.add(allocation.invoice_id);
    }
  });

  // 过滤掉已被其他分配项选择的账单
  return customerInvoices.value.filter(invoice =>
    !allocatedInvoiceIds.has(invoice.id)
  );
};

// 添加分配
const addAllocation = () => {
  const newAllocation: ExtendedAllocation = {
    invoice_id: 0,
    amount: 0,
    discount: {
      type: 'discount',
      amount: 0,
      reason: '',
      enabled: false,
    },
  };
  paymentForm.value.allocations?.push(newAllocation as any);
};

// 删除分配
const removeAllocation = (index: number) => {
  paymentForm.value.allocations?.splice(index, 1);
};

// 获取分配项的未付金额
const getAllocationUnpaidAmount = (allocation: ExtendedAllocation) => {
  const invoice = customerInvoices.value.find(inv => inv.id === allocation.invoice_id);
  if (invoice) {
    return parseFloat(invoice.amount) - parseFloat(invoice.paid_amount);
  }
  return 0;
};

// 计算分配项的差额
const getAllocationGap = (allocation: ExtendedAllocation) => {
  const unpaidAmount = getAllocationUnpaidAmount(allocation);
  return Math.max(0, unpaidAmount - allocation.amount);
};

// 切换分配项的优惠减免状态
const toggleAllocationDiscount = (index: number) => {
  if (!paymentForm.value.allocations || index >= paymentForm.value.allocations.length) {
    console.error('Invalid allocation index:', index);
    return;
  }

  const allocation = paymentForm.value.allocations[index] as ExtendedAllocation;

  if (!allocation) {
    console.error('Allocation not found at index:', index);
    return;
  }

  // 确保 discount 字段存在
  if (!allocation.discount) {
    allocation.discount = {
      type: 'discount',
      amount: 0,
      reason: '',
      enabled: false,
    };
  }

  // 切换启用状态
  allocation.discount.enabled = !allocation.discount.enabled;

  if (allocation.discount.enabled) {
    // 自动设置减免金额为差额
    allocation.discount.amount = getAllocationGap(allocation);
  } else {
    // 清空减免信息
    allocation.discount.amount = 0;
    allocation.discount.reason = '';
  }

  // 强制触发响应式更新
  paymentForm.value.allocations = [...paymentForm.value.allocations];
};

// 自动分配还款
const handleAutoAllocate = async () => {
  try {
    // 验证必要条件
    if (!paymentForm.value.customer_id) {
      message.error('请先选择客户');
      return;
    }

    if (!paymentForm.value.amount || paymentForm.value.amount <= 0) {
      message.error('请先输入有效的还款金额');
      return;
    }

    if (customerInvoices.value.length === 0) {
      message.error('该客户暂无未付清的账单');
      return;
    }

    // 如果已有分配项，询问用户是否覆盖
    if (paymentForm.value.allocations && paymentForm.value.allocations.length > 0) {
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: '确认自动分配',
          content: '当前已有手动分配项，自动分配将清空现有分配并重新分配。是否继续？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });

      if (!confirmed) {
        return;
      }
    }

    autoAllocateLoading.value = true;

    // 清空现有分配
    paymentForm.value.allocations = [];

    // 按账单日期排序（最早的优先）
    const sortedInvoices = [...customerInvoices.value].sort((a, b) => {
      return new Date(a.invoice_date).getTime() - new Date(b.invoice_date).getTime();
    });

    let remainingAmount = Number(paymentForm.value.amount);
    const newAllocations: ExtendedAllocation[] = [];

    // 按最早账单优先的策略分配
    for (const invoice of sortedInvoices) {
      if (remainingAmount <= 0) break;

      const unpaidAmount = parseFloat(invoice.amount) - parseFloat(invoice.paid_amount);
      if (unpaidAmount <= 0) continue;

      const allocationAmount = Math.min(remainingAmount, unpaidAmount);

      // 创建完整的 ExtendedAllocation 结构
      const newAllocation: ExtendedAllocation = {
        invoice_id: invoice.id,
        amount: allocationAmount,
        discount: {
          type: 'discount',
          amount: 0,
          reason: '',
          enabled: false,
        },
      };

      newAllocations.push(newAllocation);
      remainingAmount -= allocationAmount;
    }

    // 应用分配结果
    paymentForm.value.allocations = newAllocations as any;

    // 显示分配结果
    const totalAllocated = newAllocations.reduce((sum, allocation) => sum + allocation.amount, 0);
    const allocatedCount = newAllocations.length;

    if (remainingAmount > 0) {
      message.success(`自动分配完成！已分配 ${allocatedCount} 个账单，金额 ¥${totalAllocated.toFixed(2)}，剩余 ¥${remainingAmount.toFixed(2)} 未分配`);
    } else {
      message.success(`自动分配完成！已分配 ${allocatedCount} 个账单，金额 ¥${totalAllocated.toFixed(2)}`);
    }

  } catch (error) {
    console.error('自动分配失败:', error);
    message.error('自动分配失败，请重试');
  } finally {
    autoAllocateLoading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    formLoading.value = true;

    const paymentAmount = Number(paymentForm.value.amount) || 0;
    const allocatedAmount = totalAllocated.value;
    const unpaidAmount = totalUnpaidAmount.value;

    // 基础验证
    if (paymentAmount <= 0) {
      message.error('请输入有效的还款金额');
      return;
    }

    if (paymentAmount > unpaidAmount) {
      message.error(`还款金额不能超过未付账单总额 ¥${unpaidAmount.toFixed(2)}`);
      return;
    }

    // 验证分配金额总和不超过还款金额
    if (allocatedAmount > paymentAmount) {
      message.error('分配金额总和不能超过还款金额');
      return;
    }

    // 计算未分配金额
    const unallocatedAmount = paymentAmount - allocatedAmount;

    // 计算优惠减免信息
    const discountInfo = calculateDiscountInfo();

    // 资金分配完整性验证：确保所有资金都有明确去向
    if (unallocatedAmount > 0) {
      const remainingUnallocated = unallocatedAmount - discountInfo.totalAmount;

      if (remainingUnallocated > 0.01) { // 允许1分钱的舍入误差
        message.error(
          `还款金额 ¥${paymentAmount.toFixed(2)} 大于分配总额 ¥${allocatedAmount.toFixed(2)}，` +
          `剩余 ¥${remainingUnallocated.toFixed(2)} 未分配。` +
          `请完成所有资金分配或启用优惠减免处理差额。\n\n` +
          `解决方案：\n` +
          `1. 增加分配项，将剩余资金分配到其他账单\n` +
          `2. 为现有分配项启用优惠减免，处理差额\n` +
          `3. 调整还款金额为 ¥${allocatedAmount.toFixed(2)}`
        );
        return;
      }
    }

    // 准备提交数据
    const submitData: PaymentApi.PaymentFormData = {
      ...paymentForm.value,
    };

    // 处理分配项级别的优惠减免
    if (paymentForm.value.allocations) {
      // 验证启用了优惠减免的分配项
      for (let i = 0; i < paymentForm.value.allocations.length; i++) {
        const allocation = paymentForm.value.allocations[i] as ExtendedAllocation;
        if (allocation.discount?.enabled) {
          if (!allocation.discount.reason.trim()) {
            message.error(`分配项 ${i + 1} 启用了优惠减免但未填写减免原因`);
            return;
          }
          if (allocation.discount.amount <= 0) {
            message.error(`分配项 ${i + 1} 的减免金额必须大于0`);
            return;
          }
        }
      }

      // 如果有优惠减免，合并为全局减免（兼容后端API）
      if (discountInfo.totalAmount > 0) {
        // 使用第一个启用减免的分配项的类型，或默认为折扣
        const firstDiscountAllocation = paymentForm.value.allocations.find(
          (alloc: ExtendedAllocation) => alloc.discount?.enabled
        ) as ExtendedAllocation;

        submitData.discount = {
          type: firstDiscountAllocation?.discount?.type || 'discount',
          amount: discountInfo.totalAmount,
          reason: discountInfo.reasons.join('; '),
        };
      }

      // 清理分配项数据，移除前端扩展字段
      submitData.allocations = paymentForm.value.allocations.map((alloc: ExtendedAllocation) => ({
        invoice_id: alloc.invoice_id,
        amount: alloc.amount,
      }));
    }

    const createdPayment = await createPaymentApi(submitData);

    // 如果有临时文件，上传它们
    if (tempFiles.value.length > 0) {
      try {
        await uploadTempFiles(createdPayment.id);

        // 根据是否有减免显示不同的成功消息（包含附件信息）
        if (submitData.discount) {
          const discountTypeName = discountTypes.find(t => t.value === submitData.discount!.type)?.label || '优惠减免';
          const allocatedCount = paymentForm.value.allocations?.length || 0;
          const discountCount = paymentForm.value.allocations?.filter((alloc: ExtendedAllocation) => alloc.discount?.enabled).length || 0;
          message.success(`还款记录创建成功！已处理 ${allocatedCount} 个账单，其中 ${discountCount} 个账单包含 ${discountTypeName}，总减免 ¥${submitData.discount.amount.toFixed(2)}，已上传 ${tempFiles.value.length} 个附件`);
        } else {
          const allocatedCount = paymentForm.value.allocations?.length || 0;
          message.success(`还款记录创建成功！已处理 ${allocatedCount} 个账单，总金额 ¥${allocatedAmount.toFixed(2)}，已上传 ${tempFiles.value.length} 个附件`);
        }
      } catch (uploadError) {
        message.warning('还款记录创建成功，但部分附件上传失败，请在编辑页面重新上传');
      }
    } else {
      // 根据是否有减免显示不同的成功消息
      if (submitData.discount) {
        const discountTypeName = discountTypes.find(t => t.value === submitData.discount!.type)?.label || '优惠减免';
        const allocatedCount = paymentForm.value.allocations?.length || 0;
        const discountCount = paymentForm.value.allocations?.filter((alloc: ExtendedAllocation) => alloc.discount?.enabled).length || 0;
        message.success(`还款记录创建成功！已处理 ${allocatedCount} 个账单，其中 ${discountCount} 个账单包含 ${discountTypeName}，总减免 ¥${submitData.discount.amount.toFixed(2)}`);
      } else if (allocatedAmount < paymentAmount) {
        const unallocatedAmount = paymentAmount - allocatedAmount;
        message.success(`还款记录创建成功！已分配 ¥${allocatedAmount.toFixed(2)}，剩余 ¥${unallocatedAmount.toFixed(2)} 未分配`);
      } else {
        const allocatedCount = paymentForm.value.allocations?.length || 0;
        message.success(`还款记录创建成功！已处理 ${allocatedCount} 个账单，总金额 ¥${allocatedAmount.toFixed(2)}`);
      }
    }

    formVisible.value = false;
    fetchPayments();
  } catch (error) {
    console.error('保存还款失败:', error);
    message.error('保存还款失败，请重试');
  } finally {
    formLoading.value = false;
  }
};

// 删除还款
const handleDelete = async (payment: PaymentApi.Payment) => {
  try {
    await deletePaymentApi(payment.id);
    message.success('还款记录删除成功');
    fetchPayments();
  } catch (error) {
    console.error('删除还款失败:', error);
  }
};

// 查看还款详情
const viewPaymentDetail = (payment: PaymentApi.Payment) => {
  router.push(`/payment/detail/${payment.id}`);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPayments();
  fetchCustomers();
  fetchStores();
});
</script>

<template>
  <div class="p-4">
    <Card title="还款管理">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <Space wrap>
          <Select
            v-model:value="searchForm.customer_id"
            placeholder="选择客户"
            style="width: 200px"
            allow-clear
            show-search
            :filter-option="(input: string, option: any) => 
              option.label.toLowerCase().includes(input.toLowerCase())
            "
          >
            <Select.Option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
              :label="customer.name"
            >
              {{ customer.name }}
            </Select.Option>
          </Select>
          <Select
            v-model:value="searchForm.payment_method"
            placeholder="选择还款方式"
            style="width: 140px"
            allow-clear
          >
            <Select.Option
              v-for="method in paymentMethods"
              :key="method.value"
              :value="method.value"
            >
              {{ method.label }}
            </Select.Option>
          </Select>
          <DatePicker
            v-model:value="searchForm.start_date"
            placeholder="开始日期"
            value-format="YYYY-MM-DD"
          />
          <DatePicker
            v-model:value="searchForm.end_date"
            placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
          <Button type="primary" @click="handleSearch">
            <AntDesignSearchOutlined />
            搜索
          </Button>
          <Button @click="handleReset">重置</Button>
          <Button type="primary" @click="showAddForm">
            <AntDesignPlusOutlined />
            记录还款
          </Button>
        </Space>
      </div>

      <!-- 还款列表表格 -->
      <Table
        :columns="columns"
        :data-source="payments"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1200 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <Space>
              <Button type="link" size="small" @click="viewPaymentDetail(record)">
                <AntDesignEyeOutlined />
                详情
              </Button>
              <Popconfirm
                title="确定要删除这个还款记录吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <Button type="link" size="small" danger>
                  <AntDesignDeleteOutlined />
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 还款表单弹窗 -->
    <Modal
      v-model:open="formVisible"
      title="记录还款"
      :confirm-loading="formLoading"
      width="800px"
      @ok="handleSubmit"
      @cancel="formVisible = false"
    >
      <Form :model="paymentForm" layout="vertical">
        <!-- 门店选择：管理员或多门店用户显示选择框 -->
        <Form.Item
          v-if="showStoreSelection"
          label="门店"
          name="store_id"
          :rules="[{ required: true, message: '请选择门店' }]"
        >
          <Select
            v-model:value="paymentForm.store_id"
            placeholder="请选择门店"
            show-search
            :filter-option="(input: string, option: any) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            "
            @change="handleStoreChange"
          >
            <Select.Option
              v-for="store in availableStores"
              :key="store.id"
              :value="store.id"
              :label="store.name"
            >
              {{ store.name }}
            </Select.Option>
          </Select>
        </Form.Item>

        <!-- 单门店用户显示固定门店信息 -->
        <div v-else-if="userStores.length === 1" class="mb-4">
          <label class="text-sm font-medium text-muted-foreground">当前门店</label>
          <div class="mt-1 p-2 bg-accent/20 border border-border rounded text-foreground">
            {{ userStores[0]?.name || '未分配门店' }}
          </div>
        </div>

        <!-- 无门店权限提示 -->
        <div v-else class="mb-4">
          <label class="text-sm font-medium text-destructive">门店权限</label>
          <div class="mt-1 p-2 bg-destructive/10 border border-destructive/20 rounded text-destructive">
            您尚未分配任何门店权限，请联系管理员
          </div>
        </div>



        <Form.Item label="客户" name="customer_id" :rules="[{ required: true, message: '请选择客户' }]">
          <Select
            v-model:value="paymentForm.customer_id"
            placeholder="请选择客户"
            show-search
            :filter-option="(input: string, option: any) => 
              option.label.toLowerCase().includes(input.toLowerCase())
            "
            @change="handleCustomerChange"
          >
            <Select.Option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
              :label="customer.name"
            >
              {{ customer.name }}
            </Select.Option>
          </Select>
        </Form.Item>
        
        <div class="grid grid-cols-2 gap-4">
          <Form.Item label="还款金额" name="amount" :rules="[{ required: true, message: '请输入还款金额' }]">
            <Input
              v-model:value="paymentForm.amount"
              type="number"
              placeholder="请输入还款金额"
              addon-before="¥"
            />
          </Form.Item>
          <Form.Item label="还款日期" name="payment_date" :rules="[{ required: true, message: '请选择还款日期' }]">
            <DatePicker
              v-model:value="paymentForm.payment_date"
              placeholder="请选择还款日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </Form.Item>
        </div>
        
        <div class="grid grid-cols-2 gap-4">
          <Form.Item label="还款方式" name="payment_method" :rules="[{ required: true, message: '请选择还款方式' }]">
            <Select v-model:value="paymentForm.payment_method" placeholder="请选择还款方式">
              <Select.Option
                v-for="method in paymentMethods"
                :key="method.value"
                :value="method.value"
              >
                {{ method.label }}
              </Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="参考号码" name="reference_number">
            <Input v-model:value="paymentForm.reference_number" placeholder="请输入参考号码" />
          </Form.Item>
        </div>
        
        <Form.Item label="备注信息" name="remarks">
          <Input.TextArea
            v-model:value="paymentForm.remarks"
            placeholder="请输入备注信息"
            :rows="2"
          />
        </Form.Item>

        <!-- 附件上传 -->
        <Form.Item label="相关附件">
          <div class="space-y-4">
            <!-- 功能说明 -->
            <div class="flex items-start space-x-2 p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
              <div class="text-orange-500 mt-0.5 text-lg">
                ℹ️
              </div>
              <div class="text-sm text-orange-700 dark:text-orange-300">
                <div class="font-medium mb-1">支持的文件类型</div>
                <div class="text-xs space-y-1">
                  <div>• 凭证：收据、银行转账凭证、支付截图</div>
                  <div>• 格式：PDF、JPG、PNG、Word、Excel</div>
                  <div>• 大小限制：单个文件最大 10MB</div>
                </div>
              </div>
            </div>

            <!-- 文件选择器 -->
            <div class="border border-dashed border-border rounded-lg p-6 bg-gradient-to-br from-background to-accent/5">
              <div class="text-center space-y-4">
                <!-- 上传图标 -->
                <div class="flex justify-center">
                  <div class="w-16 h-16 bg-orange-500/10 rounded-full flex items-center justify-center">
                    <span class="text-3xl">☁️</span>
                  </div>
                </div>

                <!-- 上传按钮和说明 -->
                <div class="space-y-2">
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx"
                    @change="handleTempFileSelect"
                    style="display: none !important; position: absolute; left: -9999px; width: 0; height: 0; opacity: 0;"
                    ref="fileInput"
                  />
                  <Button
                    type="primary"
                    size="large"
                    @click="fileInput?.click()"
                    :loading="uploadingFiles"
                    class="shadow-sm"
                  >
                    <AntDesignPlusOutlined class="mr-2" />
                    选择文件
                  </Button>
                  <div class="text-xs text-muted-foreground">
                    点击选择文件或拖拽文件到此区域
                  </div>
                </div>
              </div>

              <!-- 已选择的临时文件列表 -->
              <div v-if="tempFiles.length > 0" class="mt-6 pt-4 border-t border-border">
                <div class="flex items-center justify-between mb-3">
                  <div class="text-sm font-medium text-foreground">已选择文件 ({{ tempFiles.length }})</div>
                  <Button type="text" size="small" @click="tempFiles = []" class="text-muted-foreground hover:text-foreground">
                    清空全部
                  </Button>
                </div>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                  <div
                    v-for="(file, index) in tempFiles"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-background border border-border rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div class="flex items-center space-x-3 flex-1 min-w-0">
                      <div class="text-orange-500">
                        📄
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-sm font-medium text-foreground truncate">{{ file.name }}</div>
                        <div class="text-xs text-muted-foreground">{{ (file.size / 1024 / 1024).toFixed(2) }} MB</div>
                      </div>
                    </div>
                    <Button
                      type="text"
                      size="small"
                      danger
                      @click="removeTempFile(index)"
                      class="flex-shrink-0 ml-2"
                    >
                      <AntDesignDeleteOutlined />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已上传附件列表 -->
            <div v-if="attachments.length > 0" class="border border-border rounded-lg p-4 bg-green-50 dark:bg-green-950/20">
              <div class="flex items-center space-x-2 mb-3">
                <div class="text-green-600 dark:text-green-400 text-lg">
                  ✅
                </div>
                <div class="text-sm font-medium text-green-700 dark:text-green-300">已上传附件 ({{ attachments.length }})</div>
              </div>
              <div class="space-y-2">
                <div
                  v-for="attachment in attachments"
                  :key="attachment.id"
                  class="flex items-center justify-between p-3 bg-background border border-border rounded-lg"
                >
                  <div class="flex items-center space-x-3">
                    <div class="text-green-600">
                      📄
                    </div>
                    <div>
                      <div class="text-sm font-medium text-foreground">{{ attachment.original_name }}</div>
                      <div class="text-xs text-muted-foreground">{{ attachment.file_size }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Form.Item>

        <!-- 还款分配 -->
        <div v-if="customerInvoices.length > 0">
          <Card
            title="还款分配"
            size="small"
            class="mb-4 shadow-sm border-border"
          >
            <template #extra>
              <Space>
                <Button
                  type="default"
                  size="small"
                  @click="handleAutoAllocate"
                  :loading="autoAllocateLoading"
                  class="rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <span class="mr-1">🤖</span>
                  自动分配
                </Button>
                <Button
                  type="primary"
                  size="small"
                  @click="addAllocation"
                  class="rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <AntDesignPlusOutlined class="mr-1" />
                  添加分配
                </Button>
              </Space>
            </template>

            <!-- 分配项列表 -->
            <div v-if="paymentForm.allocations && paymentForm.allocations.length > 0" class="space-y-4">
              <Card
                v-for="(allocation, index) in paymentForm.allocations"
                :key="index"
                size="small"
                class="bg-accent/5 dark:bg-accent/10 border-border shadow-sm hover:shadow-md transition-all duration-200"
              >
                <template #title>
                  <div class="flex items-center text-sm">
                    <span class="mr-2">💰</span>
                    <span class="font-medium text-foreground">分配项 {{ index + 1 }}</span>
                  </div>
                </template>

                <template #extra>
                  <Button
                    type="text"
                    danger
                    size="small"
                    @click="removeAllocation(index)"
                    class="hover:bg-destructive/10 rounded-lg transition-colors duration-150"
                  >
                    <AntDesignDeleteOutlined class="text-base" />
                  </Button>
                </template>

                <div class="space-y-4">
                  <!-- 账单选择 -->
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      <span class="text-destructive">*</span> 选择账单
                    </label>
                    <Select
                      v-model:value="allocation.invoice_id"
                      :placeholder="getAvailableInvoicesForAllocation(index).length > 0 ? '请选择要分配的账单' : '暂无可分配的账单'"
                      size="large"
                      class="w-full rounded-lg"
                      :disabled="getAvailableInvoicesForAllocation(index).length === 0"
                    >
                      <Select.Option
                        v-for="invoice in getAvailableInvoicesForAllocation(index)"
                        :key="invoice.id"
                        :value="invoice.id"
                      >
                        <div class="flex justify-between items-center">
                          <span class="font-medium">{{ invoice.invoice_number }}</span>
                          <span class="text-sm text-muted-foreground">
                            剩余: ¥{{ (parseFloat(invoice.amount) - parseFloat(invoice.paid_amount)).toFixed(2) }}
                          </span>
                        </div>
                      </Select.Option>
                    </Select>

                    <!-- 无可选账单时的提示 -->
                    <div v-if="getAvailableInvoicesForAllocation(index).length === 0" class="mt-1 text-xs text-muted-foreground">
                      所有账单都已分配，请删除其他分配项或添加新账单
                    </div>
                  </div>

                  <!-- 分配金额 -->
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-1">
                      <span class="text-destructive">*</span> 分配金额
                    </label>
                    <Input
                      v-model:value="allocation.amount"
                      type="number"
                      placeholder="请输入分配金额"
                      addon-before="¥"
                      size="large"
                      min="0.01"
                      step="0.01"
                      class="rounded-lg"
                    />
                  </div>

                  <!-- 优惠减免控制 -->
                  <div v-if="allocation.invoice_id && allocation.amount > 0 && getAllocationGap(allocation as ExtendedAllocation) > 0">
                    <div class="border-t border-border pt-3">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                          <span class="text-sm font-medium text-foreground mr-2">优惠减免</span>
                          <span class="text-xs text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded">
                            差额 ¥{{ getAllocationGap(allocation as ExtendedAllocation).toFixed(2) }}
                          </span>
                        </div>
                        <Button
                          type="text"
                          size="small"
                          @click="toggleAllocationDiscount(index)"
                          :class="[(allocation as ExtendedAllocation).discount?.enabled ? 'text-green-600' : 'text-gray-400']"
                        >
                          {{ (allocation as ExtendedAllocation).discount?.enabled ? '✓ 已启用' : '启用减免' }}
                        </Button>
                      </div>

                      <!-- 减免详情 -->
                      <div v-if="(allocation as ExtendedAllocation).discount?.enabled" class="space-y-3 bg-green-50 dark:bg-green-950/20 p-3 rounded-lg">
                        <div class="grid grid-cols-2 gap-3">
                          <div>
                            <label class="block text-xs font-medium text-foreground mb-1">减免类型</label>
                            <Select
                              v-model:value="(allocation as ExtendedAllocation).discount!.type"
                              size="small"
                              class="w-full"
                            >
                              <Select.Option
                                v-for="type in discountTypes"
                                :key="type.value"
                                :value="type.value"
                              >
                                {{ type.label }}
                              </Select.Option>
                            </Select>
                          </div>
                          <div>
                            <label class="block text-xs font-medium text-foreground mb-1">减免金额</label>
                            <Input
                              v-model:value="(allocation as ExtendedAllocation).discount!.amount"
                              type="number"
                              size="small"
                              addon-before="¥"
                              :max="getAllocationGap(allocation as ExtendedAllocation)"
                              min="0.01"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div>
                          <label class="block text-xs font-medium text-foreground mb-1">减免原因</label>
                          <Input.TextArea
                            v-model:value="(allocation as ExtendedAllocation).discount!.reason"
                            placeholder="请输入减免原因"
                            :rows="2"
                            size="small"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            <!-- 空状态提示 -->
            <Card
              v-else
              size="small"
              class="border-dashed border-2 border-border bg-muted/30"
            >
              <div class="text-center py-6">
                <div class="text-3xl mb-2">💸</div>
                <div class="text-muted-foreground mb-1">暂无还款分配</div>
                <div class="text-sm text-muted-foreground/70">点击上方"添加分配"按钮开始分配还款</div>
              </div>
            </Card>

            <!-- 分配统计 -->
            <Card
              v-if="paymentForm.allocations && paymentForm.allocations.length > 0"
              size="small"
              class="mt-4 bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800 shadow-sm"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <span class="text-2xl mr-3">📊</span>
                  <div>
                    <div class="text-sm text-foreground/80">分配统计</div>
                    <div class="text-xs text-muted-foreground">共 {{ paymentForm.allocations.length }} 项分配</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                    ¥{{ (paymentForm.allocations?.reduce((sum, allocation) => sum + (Number(allocation.amount) || 0), 0) || 0).toFixed(2) }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    / 总金额 ¥{{ (Number(paymentForm.amount) || 0).toFixed(2) }}
                  </div>
                </div>
              </div>

              <!-- 进度条 -->
              <div class="mt-3">
                <div class="flex justify-between text-xs text-muted-foreground mb-1">
                  <span>分配进度</span>
                  <span>{{ Math.min(100, ((paymentForm.allocations?.reduce((sum, allocation) => sum + (Number(allocation.amount) || 0), 0) || 0) / (Number(paymentForm.amount) || 1) * 100)).toFixed(1) }}%</span>
                </div>
                <div class="w-full bg-muted rounded-full h-2">
                  <div
                    class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
                    :style="{ width: Math.min(100, ((paymentForm.allocations?.reduce((sum, allocation) => sum + (Number(allocation.amount) || 0), 0) || 0) / (Number(paymentForm.amount) || 1) * 100)) + '%' }"
                  ></div>
                </div>
              </div>
            </Card>
          </Card>
        </div>


      </Form>
    </Modal>
  </div>
</template>
