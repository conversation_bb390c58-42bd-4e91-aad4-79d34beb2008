<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { Card, Button, Table, Space, Modal, Form, Input, Switch, message, Popconfirm } from 'ant-design-vue';
import { AntDesignPlusOutlined, AntDesignEditOutlined, AntDesignDeleteOutlined } from '@vben/icons';

import {
  getStoreListApi,
  createStoreApi,
  updateStoreApi,
  deleteStoreApi,
  type StoreApi,
} from '#/api/modules/store';

// 响应式数据
const loading = ref(false);
const stores = ref<StoreApi.Store[]>([]);

// 门店表单
const storeForm = ref<StoreApi.StoreFormData>({
  name: '',
  code: '',
  address: '',
  phone: '',
  description: '',
  is_active: true,
});

// 表单状态
const formVisible = ref(false);
const formLoading = ref(false);
const editingStore = ref<StoreApi.Store | null>(null);

// 表格列定义
const columns = [
  {
    title: '门店名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: '门店编码',
    dataIndex: 'code',
    key: 'code',
    width: 120,
  },
  {
    title: '联系地址',
    dataIndex: 'address',
    key: 'address',
    ellipsis: true,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 130,
  },
  {
    title: '状态',
    dataIndex: 'is_active',
    key: 'is_active',
    width: 80,
    customRender: ({ text }: { text: boolean }) => {
      return text ? '启用' : '禁用';
    },
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160,
    customRender: ({ text }: { text: string }) => {
      return new Date(text).toLocaleString();
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right' as const,
  },
];

// 获取门店列表
const fetchStores = async () => {
  try {
    loading.value = true;
    stores.value = await getStoreListApi();
  } catch (error) {
    console.error('获取门店列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 显示新增表单
const showAddForm = () => {
  editingStore.value = null;
  storeForm.value = {
    name: '',
    code: '',
    address: '',
    phone: '',
    description: '',
    is_active: true,
  };
  formVisible.value = true;
};

// 显示编辑表单
const showEditForm = (store: StoreApi.Store) => {
  editingStore.value = store;
  storeForm.value = {
    name: store.name,
    code: store.code,
    address: store.address || '',
    phone: store.phone || '',
    description: store.description || '',
    is_active: store.is_active,
  };
  formVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    formLoading.value = true;
    
    if (editingStore.value) {
      // 编辑门店
      await updateStoreApi(editingStore.value.id, storeForm.value);
      message.success('门店信息更新成功');
    } else {
      // 新增门店
      await createStoreApi(storeForm.value);
      message.success('门店创建成功');
    }
    
    formVisible.value = false;
    fetchStores();
  } catch (error) {
    console.error('保存门店失败:', error);
  } finally {
    formLoading.value = false;
  }
};

// 删除门店
const handleDelete = async (store: StoreApi.Store) => {
  try {
    await deleteStoreApi(store.id);
    message.success('门店删除成功');
    fetchStores();
  } catch (error) {
    console.error('删除门店失败:', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchStores();
});
</script>

<template>
  <div class="p-4">
    <Card title="门店管理">
      <!-- 操作区域 -->
      <div class="mb-4">
        <Button type="primary" @click="showAddForm">
          <AntDesignPlusOutlined />
          新增门店
        </Button>
      </div>

      <!-- 门店列表表格 -->
      <Table
        :columns="columns"
        :data-source="stores"
        :loading="loading"
        :pagination="false"
        :scroll="{ x: 1000 }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <Space>
              <Button type="link" size="small" @click="showEditForm(record)">
                <AntDesignEditOutlined />
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个门店吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <Button type="link" size="small" danger>
                  <AntDesignDeleteOutlined />
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 门店表单弹窗 -->
    <Modal
      v-model:open="formVisible"
      :title="editingStore ? '编辑门店' : '新增门店'"
      :confirm-loading="formLoading"
      width="600px"
      @ok="handleSubmit"
      @cancel="formVisible = false"
    >
      <Form :model="storeForm" layout="vertical">
        <Form.Item label="门店名称" name="name" :rules="[{ required: true, message: '请输入门店名称' }]">
          <Input v-model:value="storeForm.name" placeholder="请输入门店名称" />
        </Form.Item>
        <Form.Item label="门店编码" name="code" :rules="[{ required: true, message: '请输入门店编码' }]">
          <Input v-model:value="storeForm.code" placeholder="请输入门店编码" />
        </Form.Item>
        <Form.Item label="联系地址" name="address">
          <Input v-model:value="storeForm.address" placeholder="请输入联系地址" />
        </Form.Item>
        <Form.Item label="联系电话" name="phone">
          <Input v-model:value="storeForm.phone" placeholder="请输入联系电话" />
        </Form.Item>
        <Form.Item label="门店描述" name="description">
          <Input.TextArea v-model:value="storeForm.description" placeholder="请输入门店描述" :rows="3" />
        </Form.Item>
        <Form.Item label="是否启用" name="is_active">
          <Switch v-model:checked="storeForm.is_active" />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
