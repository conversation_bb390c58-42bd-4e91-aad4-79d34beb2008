<template>
  <Modal
    :open="visible"
    title="分配门店权限"
    width="700px"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirm-loading="loading"
  >
    <div class="mt-4">
      <!-- 用户信息显示 -->
      <div class="mb-6 p-4 bg-accent/20 rounded-lg border border-border">
        <h3 class="text-lg font-medium mb-2 text-foreground">用户信息</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-muted-foreground">姓名：</span>
            <span class="font-medium text-foreground">{{ userData?.name }}</span>
          </div>
          <div>
            <span class="text-muted-foreground">用户名：</span>
            <span class="font-medium text-foreground">{{ userData?.username }}</span>
          </div>
          <div>
            <span class="text-muted-foreground">邮箱：</span>
            <span class="font-medium text-foreground">{{ userData?.email }}</span>
          </div>
          <div>
            <span class="text-muted-foreground">当前角色：</span>
            <div class="inline-flex space-x-1">
              <Tag
                v-for="role in userData?.roles"
                :key="role.id"
                :color="getRoleColor(role.slug)"
                size="small"
              >
                {{ role.name }}
              </Tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 门店分配 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4 text-foreground">门店权限分配</h3>
        
        <Form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
        >
          <!-- 使用FormItem进行验证，但checkbox不被form收集 -->
          <FormItem name="stores" label="门店权限分配">
            <!-- 隐藏的input用于表单验证 -->
            <input type="hidden" :value="formData.stores.join(',')" />

            <!-- 实际的checkbox区域，使用FormItemRest避免被form收集 -->
            <FormItemRest>
              <div class="space-y-3">
                <div
                  v-for="store in stores"
                  :key="store.id"
                  class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div class="flex items-center space-x-3">
                    <Checkbox
                      :checked="isStoreSelected(store.id)"
                      @change="(e) => handleStoreChange(store.id, e.target.checked)"
                    />
                    <div>
                      <div class="font-medium text-foreground">{{ store.name }}</div>
                      <div class="text-sm text-muted-foreground">
                        编码：{{ store.code }}
                        <span v-if="store.address" class="ml-2">
                          地址：{{ store.address }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 无门店提示 -->
                <div v-if="!stores || stores.length === 0" class="text-center py-8 text-muted-foreground">
                  <MdiInformation class="text-4xl mb-2" />
                  <div>暂无可分配的门店</div>
                </div>
              </div>
            </FormItemRest>
          </FormItem>
        </Form>
      </div>

      <!-- 当前门店分配显示 -->
      <div v-if="userData?.stores?.length" class="mb-4">
        <div class="text-sm text-muted-foreground mb-2">当前门店分配：</div>
        <div class="space-x-2">
          <Tag
            v-for="store in userData.stores"
            :key="store.id"
            color="blue"
          >
            {{ store.name }}
          </Tag>
        </div>
      </div>

      <!-- 操作提示 -->
      <div class="text-sm bg-primary/10 border border-primary/20 p-3 rounded-lg">
        <div class="flex items-start space-x-2">
          <MdiInformation class="text-primary mt-0.5" />
          <div>
            <div class="font-medium text-primary mb-1">操作说明：</div>
            <ul class="space-y-1 text-primary/80">
              <li>• 选择用户可以访问的门店</li>
              <li>• 用户在门店中的权限级别由其角色决定（店长/店员）</li>
              <li>• 至少需要为普通用户分配一个门店才能正常使用系统</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue';
import {
  Modal,
  Form,
  FormItem,
  FormItemRest,
  Checkbox,
  Tag,
  message,
} from 'ant-design-vue';
import { MdiInformation } from '@vben/icons';

import { updateUserStoresApi, type UserApi } from '#/api/modules/user';
import type { StoreApi } from '#/api/modules/store';

interface Props {
  visible: boolean;
  userData: UserApi.User | null;
  stores: StoreApi.Store[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();
const loading = ref(false);

// 表单数据
const formData = reactive({
  stores: [] as number[],
});

// 表单验证规则
const rules = {
  stores: [
    {
      validator: (_rule: any, value: any[]) => {
        // 检查被编辑用户是否为管理员角色
        const isTargetUserAdmin = props.userData?.roles?.some(role => role.slug === 'admin');

        // 管理员用户可以不分配门店（因为管理员可以访问所有门店）
        if (isTargetUserAdmin) {
          return Promise.resolve();
        }

        // 使用formData.stores而不是value，因为value可能不是最新的
        const currentStores = formData.stores;

        // 普通用户必须分配至少一个门店
        if (!currentStores || currentStores.length === 0) {
          return Promise.reject(new Error('普通用户必须分配至少一个门店'));
        }

        return Promise.resolve();
      },
      trigger: ['change', 'blur'],
    },
  ],
};

// 获取角色颜色
const getRoleColor = (roleSlug: string) => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    store_owner: 'orange',
    store_staff: 'blue',
  };
  return colorMap[roleSlug] || 'default';
};

// 检查门店是否被选中
const isStoreSelected = (storeId: number) => {
  return formData.stores.includes(storeId);
};

// 处理门店选择变化
const handleStoreChange = (storeId: number, checked: boolean) => {
  // 创建新的数组来确保响应式更新
  let newStores = [...formData.stores];

  if (checked) {
    // 添加门店
    if (!newStores.includes(storeId)) {
      newStores.push(storeId);
    }
  } else {
    // 移除门店
    const index = newStores.indexOf(storeId);
    if (index > -1) {
      newStores.splice(index, 1);
    }
  }

  // 更新响应式数据
  formData.stores = newStores;

  // 强制触发响应式更新和表单验证
  nextTick(() => {
    // 手动触发表单验证
    if (formRef.value) {
      formRef.value.validateFields(['stores']).catch(() => {
        // 验证失败时的处理
      });
    }
  });
};

// 监听用户数据变化，更新表单
watch(
  () => props.userData,
  (newData) => {
    if (newData?.stores) {
      const storeIds = newData.stores.map(store => store.id);
      formData.stores = [...storeIds]; // 创建新数组确保响应式
    } else {
      formData.stores = [];
    }
  },
  { immediate: true }
);

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    if (!props.userData) {
      message.error('用户数据不存在');
      return;
    }

    loading.value = true;

    // 更新用户门店权限
    await updateUserStoresApi(props.userData.id, {
      stores: formData.stores,
    });

    message.success('门店权限分配成功');
    emit('success');
  } catch (error: any) {
    console.error('分配门店权限失败:', error);
    
    // 处理错误信息
    let errorMessage = '分配门店权限失败';
    
    if (error?.response?.data) {
      const errorData = error.response.data;
      
      if (errorData.errors) {
        const errors = Object.values(errorData.errors).flat();
        errorMessage = errors.join('；');
      } else if (errorData.message) {
        errorMessage = errorData.message;
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    message.error(errorMessage);
  } finally {
    loading.value = false;
  }
};
</script>
