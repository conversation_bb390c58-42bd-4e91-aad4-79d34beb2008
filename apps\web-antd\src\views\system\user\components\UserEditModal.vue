<template>
  <Modal
    :open="visible"
    title="编辑用户信息"
    width="600px"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirm-loading="loading"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="mt-4"
    >
      <!-- 基本信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4 text-gray-800">基本信息</h3>
        
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="姓名" name="name">
              <Input v-model:value="formData.name" placeholder="请输入姓名" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="用户名" name="username">
              <Input v-model:value="formData.username" placeholder="请输入用户名" />
            </FormItem>
          </Col>
        </Row>

        <FormItem label="邮箱" name="email">
          <Input v-model:value="formData.email" placeholder="请输入邮箱地址" />
        </FormItem>
      </div>

      <!-- 角色分配 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4 text-gray-800">角色分配</h3>
        
        <FormItem label="用户角色" name="role_ids">
          <Select
            v-model:value="formData.role_ids"
            mode="multiple"
            placeholder="请选择用户角色"
            class="w-full"
            :disabled="isRoleEditDisabled"
          >
            <SelectOption
              v-for="role in roles"
              :key="role.id"
              :value="role.id"
            >
              <div class="flex items-center justify-between">
                <span>{{ role.name }}</span>
                <Tag :color="getRoleColor(role.slug)" size="small">
                  {{ role.slug }}
                </Tag>
              </div>
            </SelectOption>
          </Select>
          <div class="text-sm mt-2">
            <div v-if="isRoleEditDisabled" class="text-orange-600">
              ⚠️ 出于安全考虑，系统管理员不能修改自己的角色权限
            </div>
            <div v-else class="text-gray-500">
              可以为用户分配多个角色，角色权限会叠加
            </div>
          </div>
        </FormItem>
      </div>

      <!-- 当前角色显示 -->
      <div v-if="userData?.roles?.length" class="mb-4">
        <div class="text-sm text-gray-600 mb-2">当前角色：</div>
        <div class="space-x-2">
          <Tag
            v-for="role in userData.roles"
            :key="role.id"
            :color="getRoleColor(role.slug)"
          >
            {{ role.name }}
          </Tag>
        </div>
      </div>
    </Form>
  </Modal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import {
  Modal,
  Form,
  FormItem,
  Input,
  Select,
  SelectOption,
  Row,
  Col,
  Tag,
  message,
} from 'ant-design-vue';
import { useUserStore } from '@vben/stores';

import { updateUserRolesApi, type UserApi } from '#/api/modules/user';

interface Props {
  visible: boolean;
  userData: UserApi.User | null;
  roles: UserApi.Role[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();
const loading = ref(false);
const userStore = useUserStore();

// 检查是否为自我编辑
const isSelfEdit = computed(() => {
  const currentUserId = userStore.userInfo?.userId;
  const editingUserId = props.userData?.id?.toString();
  return currentUserId === editingUserId;
});

// 检查当前用户是否为管理员
const isCurrentUserAdmin = computed(() => {
  return userStore.userInfo?.roles?.includes('admin') || false;
});

// 检查被编辑用户是否为管理员
const isTargetUserAdmin = computed(() => {
  return props.userData?.roles?.some(role => role.slug === 'admin') || false;
});

// 是否禁用角色编辑（管理员不能编辑自己的角色）
const isRoleEditDisabled = computed(() => {
  return isSelfEdit.value && isCurrentUserAdmin.value && isTargetUserAdmin.value;
});

// 表单数据
const formData = reactive({
  name: '',
  username: '',
  email: '',
  role_ids: [] as number[],
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 255, message: '姓名长度不能超过255个字符', trigger: 'blur' },
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { max: 255, message: '用户名长度不能超过255个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' },
    { max: 255, message: '邮箱长度不能超过255个字符', trigger: 'blur' },
  ],
  role_ids: [
    { required: true, message: '请选择至少一个角色', trigger: 'change' },
  ],
};

// 获取角色颜色
const getRoleColor = (roleSlug: string) => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    store_owner: 'orange',
    store_staff: 'blue',
  };
  return colorMap[roleSlug] || 'default';
};

// 监听用户数据变化，更新表单
watch(
  () => props.userData,
  (newData) => {
    if (newData) {
      formData.name = newData.name;
      formData.username = newData.username;
      formData.email = newData.email;
      formData.role_ids = newData.roles?.map(role => role.id) || [];
    }
  },
  { immediate: true }
);

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  // 重置表单
  formRef.value?.resetFields();
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();
    
    if (!props.userData) {
      message.error('用户数据不存在');
      return;
    }

    loading.value = true;

    // 更新用户角色
    await updateUserRolesApi(props.userData.id, {
      role_ids: formData.role_ids,
    });

    message.success('用户信息更新成功');
    emit('success');
  } catch (error: any) {
    console.error('更新用户信息失败:', error);
    
    // 处理错误信息
    let errorMessage = '更新用户信息失败';
    
    if (error?.response?.data) {
      const errorData = error.response.data;
      
      if (errorData.errors) {
        const errors = Object.values(errorData.errors).flat();
        errorMessage = errors.join('；');
      } else if (errorData.message) {
        errorMessage = errorData.message;
      }
    } else if (error?.message) {
      errorMessage = error.message;
    }

    message.error(errorMessage);
  } finally {
    loading.value = false;
  }
};
</script>
