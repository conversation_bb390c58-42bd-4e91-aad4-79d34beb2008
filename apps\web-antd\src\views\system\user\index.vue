<template>
  <div class="p-4">
    <Card>
      <template #title>
        <div class="flex items-center justify-between">
          <span class="text-lg font-semibold">用户管理</span>
          <div class="flex items-center space-x-4">
            <!-- 搜索框 -->
            <Input
              v-model:value="searchForm.search"
              placeholder="搜索用户名、姓名或邮箱"
              class="w-64"
              @press-enter="handleSearch"
            >
              <template #suffix>
                <Button type="text" @click="handleSearch">
                  <AntDesignSearchOutlined />
                </Button>
              </template>
            </Input>
            
            <!-- 角色筛选 -->
            <Select
              v-model:value="searchForm.role"
              placeholder="筛选角色"
              class="w-32"
              allow-clear
              @change="handleSearch"
            >
              <SelectOption value="admin">管理员</SelectOption>
              <SelectOption value="store_owner">店长</SelectOption>
              <SelectOption value="store_staff">店员</SelectOption>
            </Select>
          </div>
        </div>
      </template>

      <!-- 用户列表表格 -->
      <Table
        :columns="columns"
        :data-source="userList"
        :loading="loading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 角色列显示 -->
          <template v-if="column.key === 'roles'">
            <div class="space-y-1">
              <Tag
                v-for="role in record.roles"
                :key="role.id"
                :color="getRoleColor(role.slug)"
              >
                {{ role.name }}
              </Tag>
            </div>
          </template>

          <!-- 门店列显示 -->
          <template v-else-if="column.key === 'stores'">
            <div class="space-y-1">
              <Tag
                v-for="store in record.stores"
                :key="store.id"
                color="blue"
              >
                {{ store.name }}
              </Tag>
              <Tag v-if="!record.stores || record.stores.length === 0" color="red">
                未分配门店
              </Tag>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <div class="space-x-2">
              <Button
                type="link"
                size="small"
                :disabled="!canEditUser(record)"
                @click="handleEditUser(record)"
                :title="!canEditUser(record) ? '管理员不能编辑自己的角色权限' : ''"
              >
                <AntDesignEditOutlined />
                编辑
              </Button>
              <Button
                v-if="shouldShowStoreAssign(record)"
                type="link"
                size="small"
                @click="handleAssignStores(record)"
              >
                <MdiFileDocumentPlus />
                分配门店
              </Button>
              <span
                v-else
                class="text-xs text-gray-400"
                title="系统管理员具有全局权限，无需分配门店"
              >
                全局权限
              </span>
            </div>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 用户编辑弹窗 -->
    <UserEditModal
      v-model:visible="editModalVisible"
      :user-data="currentUser"
      :roles="roleList"
      @success="handleEditSuccess"
    />

    <!-- 门店分配弹窗 -->
    <StoreAssignModal
      v-model:visible="storeModalVisible"
      :user-data="currentUser"
      :stores="storeList"
      @success="handleAssignSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from 'vue';
import { 
  Card, 
  Table, 
  Input, 
  Button, 
  Select, 
  SelectOption, 
  Tag,
  message 
} from 'ant-design-vue';
import { 
  AntDesignSearchOutlined, 
  AntDesignEditOutlined,
  MdiFileDocumentPlus 
} from '@vben/icons';
import { useUserStore } from '@vben/stores';

import {
  getUserListApi, 
  getRoleListApi,
  type UserApi 
} from '#/api/modules/user';
import { getStoreListApi, type StoreApi } from '#/api/modules/store';

import UserEditModal from './components/UserEditModal.vue';
import StoreAssignModal from './components/StoreAssignModal.vue';

// 响应式数据
const loading = ref(false);
const userList = ref<UserApi.User[]>([]);
const roleList = ref<UserApi.Role[]>([]);
const storeList = ref<StoreApi.Store[]>([]);
const userStore = useUserStore();

// 检查是否可以编辑用户（防止管理员编辑自己）
const canEditUser = (user: UserApi.User) => {
  const currentUserId = userStore.userInfo?.userId;
  const targetUserId = user.id.toString();
  const isCurrentUserAdmin = userStore.userInfo?.roles?.includes('admin') || false;
  const isTargetUserAdmin = user.roles?.some(role => role.slug === 'admin') || false;

  // 如果是管理员编辑自己，则不允许编辑角色相关功能
  if (currentUserId === targetUserId && isCurrentUserAdmin && isTargetUserAdmin) {
    return false;
  }

  return true;
};

// 检查是否需要显示门店分配按钮（admin用户不需要门店分配）
const shouldShowStoreAssign = (user: UserApi.User) => {
  const isTargetUserAdmin = user.roles?.some(role => role.slug === 'admin') || false;
  // admin用户不需要门店分配，因为admin具有全局权限
  return !isTargetUserAdmin;
};
const editModalVisible = ref(false);
const storeModalVisible = ref(false);
const currentUser = ref<UserApi.User | null>(null);

// 搜索表单
const searchForm = reactive({
  search: '',
  role: undefined as string | undefined,
  page: 1,
  per_page: 15,
});

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200,
  },
  {
    title: '角色',
    key: 'roles',
    width: 150,
  },
  {
    title: '所属门店',
    key: 'stores',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
];

// 获取角色颜色
const getRoleColor = (roleSlug: string) => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    store_owner: 'orange',
    store_staff: 'blue',
  };
  return colorMap[roleSlug] || 'default';
};

// 获取用户列表
const fetchUserList = async () => {
  try {
    loading.value = true;
    const response = await getUserListApi(searchForm);

    // 根据实际响应结构处理数据
    if (response.data && Array.isArray(response.data)) {
      // 如果response.data直接是数组
      userList.value = response.data;
      paginationConfig.total = response.data.length;
      paginationConfig.current = 1;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      // 如果是分页响应格式
      userList.value = response.data.data;
      paginationConfig.total = response.data.total || 0;
      paginationConfig.current = response.data.current_page || 1;
    } else if (response && Array.isArray(response)) {
      // 如果response直接是数组
      userList.value = response;
      paginationConfig.total = response.length;
      paginationConfig.current = 1;
    } else {
      userList.value = [];
      paginationConfig.total = 0;
      paginationConfig.current = 1;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const response = await getRoleListApi();
    roleList.value = response;
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
};

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const response = await getStoreListApi();
    storeList.value = response;
  } catch (error) {
    console.error('获取门店列表失败:', error);
  }
};

// 搜索处理
const handleSearch = () => {
  searchForm.page = 1;
  paginationConfig.current = 1;
  fetchUserList();
};

// 表格变化处理
const handleTableChange = (pagination: any) => {
  searchForm.page = pagination.current;
  searchForm.per_page = pagination.pageSize;
  paginationConfig.current = pagination.current;
  paginationConfig.pageSize = pagination.pageSize;
  fetchUserList();
};

// 编辑用户
const handleEditUser = (user: UserApi.User) => {
  currentUser.value = user;
  editModalVisible.value = true;
};

// 分配门店
const handleAssignStores = (user: UserApi.User) => {
  currentUser.value = user;
  storeModalVisible.value = true;
};

// 编辑成功回调
const handleEditSuccess = () => {
  editModalVisible.value = false;
  fetchUserList();
  message.success('用户信息更新成功');
};

// 分配成功回调
const handleAssignSuccess = () => {
  storeModalVisible.value = false;
  fetchUserList();
  message.success('门店分配成功');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchUserList();
  fetchRoleList();
  fetchStoreList();
});
</script>
