import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '/api'),
            // Laravel后端API地址
            target: 'http://www.laravelqk.com',
            ws: true,
          },
        },
      },
    },
  };
});
